{"name": "@acumen-desktop/fap-lancedb", "version": "0.1.0", "description": "LanceDB vector database integration for FAP - AI-native multimodal lakehouse", "type": "module", "main": "fap-lancedb.js", "files": ["fap-lancedb.js", "fap-lancedb.css", "README.md"], "keywords": ["lancedb", "vector-database", "ai", "multimodal", "fap", "vanilla-js"], "author": "Acumen Desktop Software Canada Inc.", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/acumen-desktop/fap-monorepo.git", "directory": "packages/fap-lancedb"}, "engines": {"node": "^24.4.1"}, "dependencies": {"vectordb": "^0.13.0"}, "devDependencies": {}}