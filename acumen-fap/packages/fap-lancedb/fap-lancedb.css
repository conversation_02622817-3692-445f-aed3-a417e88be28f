/**
 * FAP-LanceDB: Vector Database UI Components
 * Semantic custom elements for LanceDB integration
 */

/* Database Connection Status */
lancedb-status {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

lancedb-status[data-state="connected"] {
  background: #dcfce7;
  color: #166534;
}

lancedb-status[data-state="disconnected"] {
  background: #fef2f2;
  color: #991b1b;
}

lancedb-status[data-state="connecting"] {
  background: #fef3c7;
  color: #92400e;
}

lancedb-status::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

/* Database Explorer */
lancedb-explorer {
  display: block;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

lancedb-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

lancedb-title {
  font-weight: 500;
  color: #111827;
}

lancedb-uri {
  font-size: 12px;
  color: #6b7280;
  font-family: monospace;
}

/* Tables List */
lancedb-tables {
  display: block;
  max-height: 300px;
  overflow-y: auto;
}

lancedb-table {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
}

lancedb-table:hover {
  background: #f9fafb;
}

lancedb-table:last-child {
  border-bottom: none;
}

lancedb-table-name {
  font-weight: 500;
  color: #111827;
}

lancedb-table-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #6b7280;
}

lancedb-table-count {
  background: #e5e7eb;
  padding: 2px 6px;
  border-radius: 3px;
}

lancedb-table-size {
  font-family: monospace;
}

/* Search Interface */
lancedb-search {
  display: block;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

lancedb-search-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

lancedb-search-input {
  display: flex;
  padding: 12px 16px;
  gap: 8px;
}

lancedb-search-input input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
}

lancedb-search-input select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: white;
}

lancedb-search-input button {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

lancedb-search-input button:hover {
  background: #2563eb;
}

lancedb-search-input button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Search Results */
lancedb-results {
  display: block;
  max-height: 400px;
  overflow-y: auto;
}

lancedb-result {
  display: block;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
}

lancedb-result:last-child {
  border-bottom: none;
}

lancedb-result-score {
  display: inline-block;
  background: #e0f2fe;
  color: #0369a1;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: 500;
  margin-bottom: 4px;
}

lancedb-result-content {
  display: block;
  color: #111827;
  margin-bottom: 4px;
}

lancedb-result-metadata {
  display: block;
  font-size: 12px;
  color: #6b7280;
}

/* Vector Visualization */
lancedb-vector {
  display: block;
  font-family: monospace;
  font-size: 10px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 8px;
  max-height: 100px;
  overflow-y: auto;
}

lancedb-vector-dimension {
  display: inline-block;
  margin-right: 4px;
  color: #6b7280;
}

lancedb-vector-value {
  display: inline-block;
  margin-right: 8px;
  color: #111827;
}

/* Embedding Service */
embedding-service {
  display: block;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

embedding-service-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

embedding-service-model {
  font-size: 12px;
  color: #6b7280;
  font-family: monospace;
}

embedding-input {
  display: flex;
  padding: 12px 16px;
  gap: 8px;
}

embedding-input textarea {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  resize: vertical;
  min-height: 60px;
}

embedding-input button {
  padding: 8px 16px;
  background: #059669;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  align-self: flex-start;
}

embedding-input button:hover {
  background: #047857;
}

embedding-output {
  display: block;
  padding: 12px 16px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

embedding-vector {
  display: block;
  font-family: monospace;
  font-size: 10px;
  color: #374151;
  max-height: 120px;
  overflow-y: auto;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 8px;
}

/* Similarity Matrix */
similarity-matrix {
  display: grid;
  gap: 2px;
  padding: 8px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
}

similarity-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 2px;
  font-size: 10px;
  font-weight: 500;
  color: white;
  min-height: 20px;
}

similarity-cell[data-similarity="high"] {
  background: #dc2626;
}

similarity-cell[data-similarity="medium"] {
  background: #f59e0b;
}

similarity-cell[data-similarity="low"] {
  background: #10b981;
}

/* Schema Display */
lancedb-schema {
  display: block;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 12px;
}

lancedb-schema-field {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 0;
  border-bottom: 1px solid #e5e7eb;
}

lancedb-schema-field:last-child {
  border-bottom: none;
}

lancedb-schema-name {
  font-weight: 500;
  color: #111827;
}

lancedb-schema-type {
  font-size: 12px;
  color: #6b7280;
  font-family: monospace;
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Progress Bars */
lancedb-progress {
  display: block;
  width: 100%;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

lancedb-progress-bar {
  display: block;
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.error {
  color: #dc2626 !important;
  background: #fef2f2 !important;
}

.success {
  color: #059669 !important;
  background: #ecfdf5 !important;
}

.warning {
  color: #d97706 !important;
  background: #fffbeb !important;
}
