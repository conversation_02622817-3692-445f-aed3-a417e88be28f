/**
 * FAP-LanceDB: Vector Database Integration
 * LanceDB wrapper for vanilla JS usage in FAP monorepo
 * 
 * Usage:
 *   const db = new window.fap.lancedb.Database('./data/vectors');
 *   await db.connect();
 *   const table = await db.createTable('documents', data);
 */

// Global namespace following FAP convention
window.fap = window.fap || {};
window.fap.lancedb = window.fap.lancedb || {};

/**
 * LanceDB Database wrapper for browser usage
 * Note: This is a client-side wrapper that communicates with a Node.js backend
 */
class LanceDatabase {
  constructor(uri, options = {}) {
    this.uri = uri;
    this.options = {
      apiEndpoint: '/api/lancedb',
      timeout: 30000,
      ...options
    };
    
    this.connected = false;
    this.tables = new Map();
  }

  /**
   * Connect to LanceDB (via backend API)
   */
  async connect() {
    try {
      const response = await fetch(`${this.options.apiEndpoint}/connect`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ uri: this.uri })
      });
      
      if (!response.ok) {
        throw new Error(`Connection failed: ${response.statusText}`);
      }
      
      const result = await response.json();
      this.connected = true;
      console.log('LanceDB connected:', result);
      return result;
      
    } catch (error) {
      console.error('LanceDB connection error:', error);
      throw error;
    }
  }

  /**
   * List all tables in the database
   */
  async listTables() {
    if (!this.connected) throw new Error('Not connected to LanceDB');
    
    const response = await fetch(`${this.options.apiEndpoint}/tables`);
    if (!response.ok) throw new Error(`Failed to list tables: ${response.statusText}`);
    
    const tables = await response.json();
    return tables;
  }

  /**
   * Create a new table
   */
  async createTable(name, data, options = {}) {
    if (!this.connected) throw new Error('Not connected to LanceDB');
    
    const response = await fetch(`${this.options.apiEndpoint}/tables/${name}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ data, options })
    });
    
    if (!response.ok) throw new Error(`Failed to create table: ${response.statusText}`);
    
    const result = await response.json();
    const table = new LanceTable(name, this);
    this.tables.set(name, table);
    return table;
  }

  /**
   * Open existing table
   */
  async openTable(name) {
    if (!this.connected) throw new Error('Not connected to LanceDB');
    
    const response = await fetch(`${this.options.apiEndpoint}/tables/${name}`);
    if (!response.ok) throw new Error(`Failed to open table: ${response.statusText}`);
    
    const table = new LanceTable(name, this);
    this.tables.set(name, table);
    return table;
  }

  /**
   * Drop a table
   */
  async dropTable(name) {
    if (!this.connected) throw new Error('Not connected to LanceDB');
    
    const response = await fetch(`${this.options.apiEndpoint}/tables/${name}`, {
      method: 'DELETE'
    });
    
    if (!response.ok) throw new Error(`Failed to drop table: ${response.statusText}`);
    
    this.tables.delete(name);
    return await response.json();
  }
}

/**
 * LanceDB Table wrapper
 */
class LanceTable {
  constructor(name, database) {
    this.name = name;
    this.database = database;
  }

  /**
   * Add data to table
   */
  async add(data) {
    const response = await fetch(`${this.database.options.apiEndpoint}/tables/${this.name}/add`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ data })
    });
    
    if (!response.ok) throw new Error(`Failed to add data: ${response.statusText}`);
    return await response.json();
  }

  /**
   * Vector similarity search
   */
  async search(vector, options = {}) {
    const searchOptions = {
      limit: 10,
      metric: 'cosine',
      ...options
    };
    
    const response = await fetch(`${this.database.options.apiEndpoint}/tables/${this.name}/search`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ vector, options: searchOptions })
    });
    
    if (!response.ok) throw new Error(`Search failed: ${response.statusText}`);
    return await response.json();
  }

  /**
   * SQL query on table
   */
  async query(sql) {
    const response = await fetch(`${this.database.options.apiEndpoint}/tables/${this.name}/query`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ sql })
    });
    
    if (!response.ok) throw new Error(`Query failed: ${response.statusText}`);
    return await response.json();
  }

  /**
   * Full-text search
   */
  async textSearch(query, options = {}) {
    const searchOptions = {
      limit: 10,
      ...options
    };
    
    const response = await fetch(`${this.database.options.apiEndpoint}/tables/${this.name}/text-search`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query, options: searchOptions })
    });
    
    if (!response.ok) throw new Error(`Text search failed: ${response.statusText}`);
    return await response.json();
  }

  /**
   * Get table schema
   */
  async schema() {
    const response = await fetch(`${this.database.options.apiEndpoint}/tables/${this.name}/schema`);
    if (!response.ok) throw new Error(`Failed to get schema: ${response.statusText}`);
    return await response.json();
  }

  /**
   * Count rows in table
   */
  async count() {
    const response = await fetch(`${this.database.options.apiEndpoint}/tables/${this.name}/count`);
    if (!response.ok) throw new Error(`Failed to count rows: ${response.statusText}`);
    const result = await response.json();
    return result.count;
  }

  /**
   * Create vector index
   */
  async createIndex(column, options = {}) {
    const indexOptions = {
      metric: 'cosine',
      ...options
    };
    
    const response = await fetch(`${this.database.options.apiEndpoint}/tables/${this.name}/index`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ column, options: indexOptions })
    });
    
    if (!response.ok) throw new Error(`Failed to create index: ${response.statusText}`);
    return await response.json();
  }
}

/**
 * Vector utilities for embeddings and similarity
 */
class VectorUtils {
  /**
   * Calculate cosine similarity between two vectors
   */
  static cosineSimilarity(a, b) {
    if (a.length !== b.length) {
      throw new Error('Vectors must have the same length');
    }
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  /**
   * Calculate Euclidean distance between two vectors
   */
  static euclideanDistance(a, b) {
    if (a.length !== b.length) {
      throw new Error('Vectors must have the same length');
    }
    
    let sum = 0;
    for (let i = 0; i < a.length; i++) {
      sum += Math.pow(a[i] - b[i], 2);
    }
    
    return Math.sqrt(sum);
  }

  /**
   * Normalize vector to unit length
   */
  static normalize(vector) {
    const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0));
    return vector.map(val => val / magnitude);
  }

  /**
   * Generate random vector (for testing)
   */
  static randomVector(dimensions) {
    return Array.from({ length: dimensions }, () => Math.random() - 0.5);
  }
}

/**
 * Embedding service for text vectorization
 */
class EmbeddingService {
  constructor(options = {}) {
    this.options = {
      apiEndpoint: '/api/embeddings',
      model: 'text-embedding-ada-002',
      ...options
    };
  }

  /**
   * Generate embeddings for text
   */
  async embed(text) {
    const response = await fetch(`${this.options.apiEndpoint}/embed`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        text, 
        model: this.options.model 
      })
    });
    
    if (!response.ok) throw new Error(`Embedding failed: ${response.statusText}`);
    const result = await response.json();
    return result.embedding;
  }

  /**
   * Generate embeddings for multiple texts
   */
  async embedBatch(texts) {
    const response = await fetch(`${this.options.apiEndpoint}/embed-batch`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        texts, 
        model: this.options.model 
      })
    });
    
    if (!response.ok) throw new Error(`Batch embedding failed: ${response.statusText}`);
    const result = await response.json();
    return result.embeddings;
  }
}

// Export to global FAP namespace
window.fap.lancedb.Database = LanceDatabase;
window.fap.lancedb.Table = LanceTable;
window.fap.lancedb.VectorUtils = VectorUtils;
window.fap.lancedb.EmbeddingService = EmbeddingService;

// Utility functions
window.fap.lancedb.utils = {
  /**
   * Create database connection
   */
  connect(uri, options = {}) {
    return new LanceDatabase(uri, options);
  },

  /**
   * Create embedding service
   */
  embeddings(options = {}) {
    return new EmbeddingService(options);
  },

  /**
   * Quick semantic search setup
   */
  async setupSemanticSearch(dbUri, tableName, documents) {
    const db = new LanceDatabase(dbUri);
    await db.connect();
    
    const embeddings = new EmbeddingService();
    
    // Generate embeddings for documents
    const documentsWithEmbeddings = await Promise.all(
      documents.map(async (doc) => ({
        ...doc,
        embedding: await embeddings.embed(doc.text)
      }))
    );
    
    // Create table with embeddings
    const table = await db.createTable(tableName, documentsWithEmbeddings);
    await table.createIndex('embedding');
    
    return { db, table, embeddings };
  }
};

console.log('FAP-LanceDB: Vector database integration loaded');
