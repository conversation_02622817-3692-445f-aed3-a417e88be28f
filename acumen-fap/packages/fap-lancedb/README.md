# FAP-LanceDB: Vector Database Integration

A vanilla JavaScript wrapper for LanceDB, the open-source AI-native multimodal lakehouse, designed for use in the FAP monorepo.

## Overview

This package provides a browser-friendly interface to LanceDB's vector database capabilities, enabling semantic search, multimodal data storage, and AI-powered data operations. It follows the FAP philosophy of semantic custom elements and vanilla JavaScript.

## Features

- **Pure Vanilla JS** - No frameworks or build steps required
- **Vector Search** - Semantic similarity search with embeddings
- **Multimodal Support** - Text, images, videos, and more
- **Hybrid Queries** - Vector similarity + full-text + SQL
- **Semantic Custom Elements** - FAP-style UI components
- **Real-time Updates** - Live search and data visualization

## Quick Start

```html
<!DOCTYPE html>
<html>
<head>
  <link rel="stylesheet" href="fap-lancedb.css">
</head>
<body>
  <lancedb-status data-state="disconnected">Disconnected</lancedb-status>
  
  <script src="fap-lancedb.js"></script>
  <script>
    // Connect to LanceDB
    const db = window.fap.lancedb.utils.connect('./data/vectors');
    
    db.connect().then(() => {
      document.querySelector('lancedb-status').setAttribute('data-state', 'connected');
      document.querySelector('lancedb-status').textContent = 'Connected';
    });
  </script>
</body>
</html>
```

## API Reference

### Database Operations

```javascript
// Create database connection
const db = new window.fap.lancedb.Database('./data/vectors');
await db.connect();

// List tables
const tables = await db.listTables();

// Create table with data
const documents = [
  { id: 1, text: "AI is transforming the world", category: "tech" },
  { id: 2, text: "Machine learning enables automation", category: "tech" }
];
const table = await db.createTable('documents', documents);

// Open existing table
const table = await db.openTable('documents');
```

### Vector Search

```javascript
// Add documents with embeddings
const embeddings = new window.fap.lancedb.EmbeddingService();
const embedding = await embeddings.embed("artificial intelligence");

// Vector similarity search
const results = await table.search(embedding, { limit: 5 });

// Text search (full-text)
const textResults = await table.textSearch("machine learning");

// SQL queries
const sqlResults = await table.query("SELECT * FROM documents WHERE category = 'tech'");
```

### Embeddings

```javascript
// Create embedding service
const embeddings = new window.fap.lancedb.EmbeddingService({
  model: 'text-embedding-ada-002'
});

// Single text embedding
const vector = await embeddings.embed("Hello world");

// Batch embeddings
const vectors = await embeddings.embedBatch([
  "First document",
  "Second document",
  "Third document"
]);
```

## Semantic Custom Elements

### Database Explorer
```html
<lancedb-explorer>
  <lancedb-header>
    <lancedb-title>Vector Database</lancedb-title>
    <lancedb-uri>./data/vectors</lancedb-uri>
    <lancedb-status data-state="connected">Connected</lancedb-status>
  </lancedb-header>
  
  <lancedb-tables>
    <lancedb-table>
      <lancedb-table-name>documents</lancedb-table-name>
      <lancedb-table-info>
        <lancedb-table-count>1,234</lancedb-table-count>
        <lancedb-table-size>45.2 MB</lancedb-table-size>
      </lancedb-table-info>
    </lancedb-table>
  </lancedb-tables>
</lancedb-explorer>
```

### Search Interface
```html
<lancedb-search>
  <lancedb-search-header>
    <h3>Semantic Search</h3>
    <lancedb-status data-state="connected">Ready</lancedb-status>
  </lancedb-search-header>
  
  <lancedb-search-input>
    <input type="text" placeholder="Search for similar content...">
    <select>
      <option value="vector">Vector Search</option>
      <option value="text">Full-text Search</option>
      <option value="sql">SQL Query</option>
    </select>
    <button>Search</button>
  </lancedb-search-input>
  
  <lancedb-results>
    <lancedb-result>
      <lancedb-result-score>0.95</lancedb-result-score>
      <lancedb-result-content>AI is transforming the world</lancedb-result-content>
      <lancedb-result-metadata>Category: tech | ID: 1</lancedb-result-metadata>
    </lancedb-result>
  </lancedb-results>
</lancedb-search>
```

### Embedding Service
```html
<embedding-service>
  <embedding-service-header>
    <h3>Text Embeddings</h3>
    <embedding-service-model>text-embedding-ada-002</embedding-service-model>
  </embedding-service-header>
  
  <embedding-input>
    <textarea placeholder="Enter text to embed..."></textarea>
    <button>Generate Embedding</button>
  </embedding-input>
  
  <embedding-output>
    <embedding-vector>[0.123, -0.456, 0.789, ...]</embedding-vector>
  </embedding-output>
</embedding-service>
```

## Integration with TerminusDB

LanceDB complements TerminusDB perfectly in your FAP monorepo:

```javascript
// Use both databases together
const lance = window.fap.lancedb.utils.connect('./data/vectors');
const terminus = window.fap.terminusdb.connect('http://localhost:6363');

// Store embeddings in LanceDB, relationships in TerminusDB
const documents = await terminus.query('SELECT * FROM documents');
const embeddings = await Promise.all(
  documents.map(doc => embeddings.embed(doc.content))
);

// Add to LanceDB for semantic search
await lance.createTable('document_embeddings', 
  documents.map((doc, i) => ({
    ...doc,
    embedding: embeddings[i]
  }))
);
```

## Vector Utilities

```javascript
// Calculate similarity between vectors
const similarity = window.fap.lancedb.VectorUtils.cosineSimilarity(vectorA, vectorB);

// Calculate distance
const distance = window.fap.lancedb.VectorUtils.euclideanDistance(vectorA, vectorB);

// Normalize vector
const normalized = window.fap.lancedb.VectorUtils.normalize(vector);

// Generate random vector for testing
const randomVec = window.fap.lancedb.VectorUtils.randomVector(1536);
```

## Semantic Search Setup

```javascript
// Quick setup for semantic search
const { db, table, embeddings } = await window.fap.lancedb.utils.setupSemanticSearch(
  './data/vectors',
  'documents',
  [
    { id: 1, text: "AI and machine learning", category: "tech" },
    { id: 2, text: "Natural language processing", category: "nlp" },
    { id: 3, text: "Computer vision applications", category: "cv" }
  ]
);

// Now you can search semantically
const results = await table.search(
  await embeddings.embed("artificial intelligence"),
  { limit: 3 }
);
```

## Backend API Requirements

This package requires a Node.js backend API to interface with LanceDB. Example endpoints:

```javascript
// Express.js backend example
app.post('/api/lancedb/connect', async (req, res) => {
  const { uri } = req.body;
  // Initialize LanceDB connection
});

app.get('/api/lancedb/tables', async (req, res) => {
  // List tables
});

app.post('/api/lancedb/tables/:name/search', async (req, res) => {
  // Vector search
});

app.post('/api/embeddings/embed', async (req, res) => {
  // Generate embeddings
});
```

## Performance Considerations

- **Indexing**: Create vector indexes for large datasets
- **Batching**: Use batch operations for bulk inserts
- **Caching**: Cache embeddings to avoid regeneration
- **Chunking**: Split large documents into smaller chunks

## Use Cases

- **Semantic Search** - Find similar documents by meaning
- **RAG Systems** - Retrieval-Augmented Generation
- **Content Recommendation** - Similar content suggestions
- **Duplicate Detection** - Find near-duplicate content
- **Classification** - Cluster and categorize content

## License

Apache-2.0 - Same as LanceDB and Goose source
