/**
 * FAP-MCP: Model Context Protocol UI Components
 * Semantic custom elements for MCP integration
 */

/* MCP Connection Status */
mcp-status {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

mcp-status[data-state="connected"] {
  background: #dcfce7;
  color: #166534;
}

mcp-status[data-state="disconnected"] {
  background: #fef2f2;
  color: #991b1b;
}

mcp-status[data-state="connecting"] {
  background: #fef3c7;
  color: #92400e;
}

mcp-status::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

/* MCP Server Registry */
mcp-registry {
  display: block;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

mcp-server {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f3f4f6;
}

mcp-server:last-child {
  border-bottom: none;
}

mcp-server-name {
  font-weight: 500;
  color: #111827;
}

mcp-server-url {
  font-size: 12px;
  color: #6b7280;
  font-family: monospace;
}

mcp-server-actions {
  display: flex;
  gap: 8px;
}

/* MCP Tools List */
mcp-tools {
  display: block;
}

mcp-tool {
  display: block;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
  background: #fafafa;
}

mcp-tool:hover {
  background: #f5f5f5;
  cursor: pointer;
}

mcp-tool-name {
  display: block;
  font-weight: 500;
  color: #111827;
  margin-bottom: 4px;
}

mcp-tool-description {
  display: block;
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 8px;
}

mcp-tool-schema {
  display: block;
  font-size: 12px;
  font-family: monospace;
  color: #4b5563;
  background: #f9fafb;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

/* MCP Resources */
mcp-resources {
  display: block;
}

mcp-resource {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  margin-bottom: 4px;
}

mcp-resource:hover {
  background: #f9fafb;
  cursor: pointer;
}

mcp-resource-uri {
  font-family: monospace;
  font-size: 12px;
  color: #4b5563;
}

mcp-resource-type {
  font-size: 10px;
  padding: 2px 6px;
  background: #e5e7eb;
  color: #374151;
  border-radius: 3px;
  text-transform: uppercase;
  font-weight: 500;
}

/* MCP Chat Integration */
mcp-chat {
  display: block;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

mcp-chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

mcp-chat-title {
  font-weight: 500;
  color: #111827;
}

mcp-chat-messages {
  display: block;
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
}

mcp-chat-input {
  display: flex;
  padding: 12px 16px;
  border-top: 1px solid #e5e7eb;
  background: #fafafa;
}

mcp-chat-input input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  margin-right: 8px;
}

mcp-chat-input button {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

mcp-chat-input button:hover {
  background: #2563eb;
}

mcp-chat-input button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Message Bubbles */
mcp-message {
  display: block;
  margin-bottom: 12px;
}

mcp-message[data-role="user"] {
  text-align: right;
}

mcp-message[data-role="assistant"] {
  text-align: left;
}

mcp-message-bubble {
  display: inline-block;
  max-width: 70%;
  padding: 8px 12px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.4;
}

mcp-message[data-role="user"] mcp-message-bubble {
  background: #3b82f6;
  color: white;
}

mcp-message[data-role="assistant"] mcp-message-bubble {
  background: #f3f4f6;
  color: #111827;
}

mcp-message[data-role="system"] mcp-message-bubble {
  background: #fef3c7;
  color: #92400e;
  font-style: italic;
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.error {
  color: #dc2626 !important;
  background: #fef2f2 !important;
}

.success {
  color: #059669 !important;
  background: #ecfdf5 !important;
}
