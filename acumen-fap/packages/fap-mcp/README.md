# FAP-MCP: Model Context Protocol Client

A vanilla JavaScript implementation of the Model Context Protocol (MCP) client, extracted from <PERSON> for use in the FAP monorepo.

## Overview

This package provides a lightweight, framework-free way to connect to MCP servers and integrate AI agent capabilities into your applications. It follows the FAP philosophy of semantic custom elements and vanilla JavaScript.

## Features

- **Pure Vanilla JS** - No frameworks or build steps required
- **WebSocket Communication** - Real-time connection to MCP servers
- **Semantic Custom Elements** - FAP-style UI components
- **Multiple Server Support** - Registry for managing multiple MCP connections
- **Tool Integration** - Call AI tools and functions
- **Resource Access** - Read files, databases, and other resources
- **Prompt Management** - Access and execute AI prompts

## Quick Start

```html
<!DOCTYPE html>
<html>
<head>
  <link rel="stylesheet" href="fap-mcp.css">
</head>
<body>
  <mcp-status data-state="disconnected">Disconnected</mcp-status>
  
  <script src="fap-mcp.js"></script>
  <script>
    // Connect to Goose MCP server
    const client = window.fap.mcp.utils.connectToGoose(3001);
    
    client.addEventListener('connected', () => {
      document.querySelector('mcp-status').setAttribute('data-state', 'connected');
      document.querySelector('mcp-status').textContent = 'Connected';
    });
    
    // Connect
    client.connect();
  </script>
</body>
</html>
```

## API Reference

### MCPClient

```javascript
// Create client
const client = new window.fap.mcp.Client('ws://localhost:3001/mcp');

// Connect
await client.connect();

// List available tools
const tools = await client.listTools();

// Call a tool
const result = await client.callTool('search_web', { query: 'AI news' });

// List resources
const resources = await client.listResources();

// Read a resource
const content = await client.readResource('file:///path/to/file.txt');

// Event listeners
client.addEventListener('connected', () => console.log('Connected!'));
client.addEventListener('disconnected', () => console.log('Disconnected!'));
```

### MCPRegistry

```javascript
// Register multiple servers
window.fap.mcp.registry.register('goose', 'ws://localhost:3001/mcp');
window.fap.mcp.registry.register('custom', 'ws://localhost:8080/mcp');

// Connect to all
await window.fap.mcp.registry.connectAll();

// List all tools from all servers
const allTools = await window.fap.mcp.registry.listAllTools();
```

## Semantic Custom Elements

### Connection Status
```html
<mcp-status data-state="connected">Connected to Goose</mcp-status>
<mcp-status data-state="disconnected">Disconnected</mcp-status>
<mcp-status data-state="connecting">Connecting...</mcp-status>
```

### Server Registry
```html
<mcp-registry>
  <mcp-server>
    <mcp-server-name>Goose</mcp-server-name>
    <mcp-server-url>ws://localhost:3001/mcp</mcp-server-url>
    <mcp-server-actions>
      <button>Connect</button>
      <button>Disconnect</button>
    </mcp-server-actions>
  </mcp-server>
</mcp-registry>
```

### Tools Display
```html
<mcp-tools>
  <mcp-tool>
    <mcp-tool-name>search_web</mcp-tool-name>
    <mcp-tool-description>Search the web for information</mcp-tool-description>
    <mcp-tool-schema>{"query": "string"}</mcp-tool-schema>
  </mcp-tool>
</mcp-tools>
```

### Chat Interface
```html
<mcp-chat>
  <mcp-chat-header>
    <mcp-chat-title>AI Assistant</mcp-chat-title>
    <mcp-status data-state="connected">Connected</mcp-status>
  </mcp-chat-header>
  
  <mcp-chat-messages>
    <mcp-message data-role="user">
      <mcp-message-bubble>Hello, can you help me?</mcp-message-bubble>
    </mcp-message>
    <mcp-message data-role="assistant">
      <mcp-message-bubble>Of course! What can I help you with?</mcp-message-bubble>
    </mcp-message>
  </mcp-chat-messages>
  
  <mcp-chat-input>
    <input type="text" placeholder="Type your message...">
    <button>Send</button>
  </mcp-chat-input>
</mcp-chat>
```

## Integration with Goose

This package is designed to work seamlessly with the Goose AI agent:

```javascript
// Connect to running Goose web server
const goose = window.fap.mcp.utils.connectToGoose(3001);
await goose.connect();

// Use Goose tools
const searchResult = await goose.callTool('web_search', { 
  query: 'latest AI developments' 
});

// Access Goose resources
const fileContent = await goose.readResource('file:///project/README.md');
```

## Error Handling

```javascript
try {
  await client.connect();
} catch (error) {
  console.error('Connection failed:', error);
}

// Handle connection events
client.addEventListener('error', (error) => {
  console.error('MCP Error:', error);
});

client.addEventListener('disconnected', () => {
  // Attempt reconnection
  setTimeout(() => client.connect(), 5000);
});
```

## Development

This package follows FAP conventions:

- **Semantic HTML** - Custom elements for self-documenting structure
- **Alphabetical CSS** - Organized by element names
- **Global Namespace** - `window.fap.mcp.*`
- **No Build Step** - Direct browser usage
- **Vanilla JavaScript** - No framework dependencies

## License

Apache-2.0 - Same as Goose source
