/**
 * FAP-MCP: Model Context Protocol Client
 * Extracted from Goose for vanilla JS usage in FAP monorepo
 * 
 * Usage:
 *   const mcp = new window.fap.mcp.Client('ws://localhost:3001');
 *   await mcp.connect();
 *   const tools = await mcp.listTools();
 */

// Global namespace following FAP convention
window.fap = window.fap || {};
window.fap.mcp = window.fap.mcp || {};

/**
 * MCP Client for connecting to Model Context Protocol servers
 */
class MCPClient {
  constructor(serverUrl, options = {}) {
    this.serverUrl = serverUrl;
    this.options = {
      reconnectAttempts: 3,
      reconnectDelay: 1000,
      timeout: 30000,
      ...options
    };
    
    this.websocket = null;
    this.connected = false;
    this.messageId = 0;
    this.pendingRequests = new Map();
    this.eventListeners = new Map();
    
    // Bind methods to preserve context
    this.onMessage = this.onMessage.bind(this);
    this.onOpen = this.onOpen.bind(this);
    this.onClose = this.onClose.bind(this);
    this.onError = this.onError.bind(this);
  }

  /**
   * Connect to MCP server
   */
  async connect() {
    return new Promise((resolve, reject) => {
      try {
        this.websocket = new WebSocket(this.serverUrl);
        this.websocket.onopen = () => {
          this.onOpen();
          resolve();
        };
        this.websocket.onmessage = this.onMessage;
        this.websocket.onclose = this.onClose;
        this.websocket.onerror = (error) => {
          this.onError(error);
          reject(error);
        };
        
        // Timeout handling
        setTimeout(() => {
          if (!this.connected) {
            this.websocket.close();
            reject(new Error('Connection timeout'));
          }
        }, this.options.timeout);
        
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Disconnect from MCP server
   */
  disconnect() {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    this.connected = false;
  }

  /**
   * Send MCP request
   */
  async sendRequest(method, params = {}) {
    if (!this.connected) {
      throw new Error('Not connected to MCP server');
    }

    const messageId = ++this.messageId;
    const message = {
      jsonrpc: '2.0',
      id: messageId,
      method,
      params
    };

    return new Promise((resolve, reject) => {
      // Store pending request
      this.pendingRequests.set(messageId, { resolve, reject });
      
      // Send message
      this.websocket.send(JSON.stringify(message));
      
      // Timeout handling
      setTimeout(() => {
        if (this.pendingRequests.has(messageId)) {
          this.pendingRequests.delete(messageId);
          reject(new Error(`Request timeout: ${method}`));
        }
      }, this.options.timeout);
    });
  }

  /**
   * List available tools from MCP server
   */
  async listTools() {
    return this.sendRequest('tools/list');
  }

  /**
   * Call a specific tool
   */
  async callTool(name, arguments_) {
    return this.sendRequest('tools/call', {
      name,
      arguments: arguments_
    });
  }

  /**
   * List available resources
   */
  async listResources() {
    return this.sendRequest('resources/list');
  }

  /**
   * Read a specific resource
   */
  async readResource(uri) {
    return this.sendRequest('resources/read', { uri });
  }

  /**
   * List available prompts
   */
  async listPrompts() {
    return this.sendRequest('prompts/list');
  }

  /**
   * Get a specific prompt
   */
  async getPrompt(name, arguments_) {
    return this.sendRequest('prompts/get', {
      name,
      arguments: arguments_
    });
  }

  /**
   * Add event listener
   */
  addEventListener(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * Remove event listener
   */
  removeEventListener(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Emit event to listeners
   */
  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Error in event listener:', error);
        }
      });
    }
  }

  // WebSocket event handlers
  onOpen() {
    this.connected = true;
    this.emit('connected');
    console.log('MCP Client connected to:', this.serverUrl);
  }

  onClose() {
    this.connected = false;
    this.emit('disconnected');
    console.log('MCP Client disconnected from:', this.serverUrl);
    
    // Reject all pending requests
    this.pendingRequests.forEach(({ reject }) => {
      reject(new Error('Connection closed'));
    });
    this.pendingRequests.clear();
  }

  onError(error) {
    this.emit('error', error);
    console.error('MCP Client error:', error);
  }

  onMessage(event) {
    try {
      const message = JSON.parse(event.data);
      
      // Handle response to our request
      if (message.id && this.pendingRequests.has(message.id)) {
        const { resolve, reject } = this.pendingRequests.get(message.id);
        this.pendingRequests.delete(message.id);
        
        if (message.error) {
          reject(new Error(message.error.message || 'MCP Error'));
        } else {
          resolve(message.result);
        }
      }
      
      // Handle notifications/events
      else if (message.method) {
        this.emit('notification', message);
      }
      
    } catch (error) {
      console.error('Error parsing MCP message:', error);
    }
  }
}

/**
 * MCP Server Registry for managing multiple servers
 */
class MCPRegistry {
  constructor() {
    this.servers = new Map();
  }

  /**
   * Register an MCP server
   */
  register(name, serverUrl, options = {}) {
    const client = new MCPClient(serverUrl, options);
    this.servers.set(name, client);
    return client;
  }

  /**
   * Get registered server
   */
  get(name) {
    return this.servers.get(name);
  }

  /**
   * Connect to all registered servers
   */
  async connectAll() {
    const connections = Array.from(this.servers.values()).map(client => 
      client.connect().catch(error => ({ error, client }))
    );
    
    const results = await Promise.allSettled(connections);
    return results;
  }

  /**
   * Disconnect from all servers
   */
  disconnectAll() {
    this.servers.forEach(client => client.disconnect());
  }

  /**
   * List all available tools from all servers
   */
  async listAllTools() {
    const toolLists = await Promise.allSettled(
      Array.from(this.servers.entries()).map(async ([name, client]) => {
        if (!client.connected) return { name, tools: [] };
        try {
          const tools = await client.listTools();
          return { name, tools: tools.tools || [] };
        } catch (error) {
          return { name, tools: [], error: error.message };
        }
      })
    );

    return toolLists
      .filter(result => result.status === 'fulfilled')
      .map(result => result.value);
  }
}

// Export to global FAP namespace
window.fap.mcp.Client = MCPClient;
window.fap.mcp.Registry = MCPRegistry;

// Create default registry instance
window.fap.mcp.registry = new MCPRegistry();

// Utility functions
window.fap.mcp.utils = {
  /**
   * Create connection to Goose MCP server
   */
  connectToGoose(port = 3001) {
    const client = new MCPClient(`ws://localhost:${port}/mcp`);
    window.fap.mcp.registry.register('goose', `ws://localhost:${port}/mcp`);
    return client;
  },

  /**
   * Create connection to custom MCP server
   */
  connectToServer(name, url, options = {}) {
    return window.fap.mcp.registry.register(name, url, options);
  }
};

console.log('FAP-MCP: Model Context Protocol client loaded');
