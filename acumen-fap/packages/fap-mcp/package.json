{"name": "@acumen-desktop/fap-mcp", "version": "0.1.0", "description": "Model Context Protocol client for FAP - extracted from Goose", "type": "module", "main": "fap-mcp.js", "files": ["fap-mcp.js", "fap-mcp.css", "README.md"], "keywords": ["mcp", "model-context-protocol", "ai", "fap", "vanilla-js"], "author": "Acumen Desktop Software Canada Inc.", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/acumen-desktop/fap-monorepo.git", "directory": "packages/fap-mcp"}, "engines": {"node": "^24.4.1"}, "dependencies": {}, "devDependencies": {}}