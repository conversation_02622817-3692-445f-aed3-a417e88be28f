[package]
name = "fap-web"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Web server utilities for FAP applications"

[dependencies]
fap-core = { path = "../fap-core", features = ["async", "config"] }

# Web server stack
axum.workspace = true
tower-http.workspace = true
tower.workspace = true
http.workspace = true
hyper.workspace = true
tokio.workspace = true

# Serialization
serde.workspace = true
serde_json.workspace = true

# Utilities
anyhow.workspace = true
tracing.workspace = true

[features]
default = ["cors", "static-files"]
cors = []
static-files = []
websockets = []
