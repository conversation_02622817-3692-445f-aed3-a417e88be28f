use crate::response::{ApiResponse, FapResponse};
use axum::Json;
use fap_core::prelude::*;

/// Standard health check handler
pub async fn health_handler() -> <PERSON>son<ApiResponse<serde_json::Value>> {
    Json(ApiResponse::success(serde_json::json!({
        "status": "healthy",
        "timestamp": FapTime::now().to_rfc3339(),
        "version": fap_core::VERSION,
        "service": "fap-web"
    })))
}

/// Standard not found handler
pub async fn not_found_handler() -> FapResponse<String> {
    FapResponse::not_found("Endpoint not found".to_string())
}

/// Standard error handler
pub async fn error_handler(error: String) -> FapResponse<String> {
    FapResponse::internal_error(error)
}

/// Simple health check function (non-axum version)
pub async fn health_check() -> Result<ApiResponse<serde_json::Value>, FapError> {
    Ok(ApiResponse::success(serde_json::json!({
        "status": "healthy",
        "timestamp": FapTime::now().to_rfc3339(),
        "version": fap_core::VERSION,
        "service": "fap-web"
    })))
}
