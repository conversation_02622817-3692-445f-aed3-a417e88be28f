/*!
# FAP Web Library

Shared web server utilities for FAP applications.
This library provides common patterns for building web APIs and serving static files.

## Features

- `cors`: Enable CORS middleware
- `static-files`: Enable static file serving
- `websockets`: Enable WebSocket support

## Usage

```rust
use fap_web::prelude::*;

#[tokio::main]
async fn main() -> FapResult<()> {
    let app = FapWebApp::new("my-app")
        .with_cors()
        .with_static_files("./static")
        .route("/api/health", get(health_handler))
        .build();
    
    app.serve("127.0.0.1:3000").await
}
```
*/

// Core modules (working)
pub mod response;
pub mod handlers;

// Complex modules (deferred for later fixes)
// pub mod middleware;  // TraceLayer generic issues
// pub mod app;         // Depends on middleware
// pub mod websocket;   // Module doesn't exist yet

/// Minimal prelude for FAP web applications
pub mod prelude {
    pub use crate::response::{FapResponse, ApiResponse};
    pub use crate::handlers::*;
    pub use fap_core::prelude::*;
    
    // Re-export essential axum types
    pub use axum::{
        extract::{Query, Path, State},
        http::StatusCode,
        response::{Html, IntoResponse, Json},
        routing::{get, post, put, delete},
        Router,
    };
}
