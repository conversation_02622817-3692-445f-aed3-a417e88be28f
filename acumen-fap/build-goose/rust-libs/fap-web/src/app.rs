use crate::middleware;
use crate::response::ApiResponse;
use axum::{Router, serve};
use fap_core::prelude::*;
use std::net::SocketAddr;
use tokio::net::TcpListener;
use tower_http::services::ServeDir;

/// FAP Web Application Builder
pub struct FapWebApp {
    name: String,
    router: Router,
    enable_cors: bool,
    static_dir: Option<String>,
}

impl FapWebApp {
    /// Create a new FAP web application
    pub fn new(name: impl Into<String>) -> Self {
        Self {
            name: name.into(),
            router: Router::new(),
            enable_cors: false,
            static_dir: None,
        }
    }
    
    /// Enable CORS middleware
    #[cfg(feature = "cors")]
    pub fn with_cors(mut self) -> Self {
        self.enable_cors = true;
        self
    }
    
    /// Add static file serving
    #[cfg(feature = "static-files")]
    pub fn with_static_files(mut self, dir: impl Into<String>) -> Self {
        self.static_dir = Some(dir.into());
        self
    }
    
    /// Add a route to the application
    pub fn route(mut self, path: &str, method_router: axum::routing::MethodRouter) -> Self {
        self.router = self.router.route(path, method_router);
        self
    }
    
    /// Nest a router at a path
    pub fn nest(mut self, path: &str, router: Router) -> Self {
        self.router = self.router.nest(path, router);
        self
    }
    
    /// Build the final application
    pub fn build(mut self) -> FapWebAppInstance {
        // Add default health endpoint
        self.router = self.router.route("/health", axum::routing::get(default_health_handler));
        
        // Add static file serving if enabled
        #[cfg(feature = "static-files")]
        if let Some(static_dir) = &self.static_dir {
            self.router = self.router.nest_service("/static", ServeDir::new(static_dir));
        }
        
        // Add CORS if enabled
        #[cfg(feature = "cors")]
        if self.enable_cors {
            self.router = self.router.layer(middleware::cors_layer());
        }
        
        // Add logging middleware
        self.router = self.router.layer(middleware::logging_layer());
        
        FapWebAppInstance {
            name: self.name,
            router: self.router,
        }
    }
}

/// Built FAP Web Application Instance
pub struct FapWebAppInstance {
    name: String,
    router: Router,
}

impl FapWebAppInstance {
    /// Serve the application on the given address
    pub async fn serve(self, addr: impl Into<SocketAddr>) -> FapResult<()> {
        let addr = addr.into();
        let listener = TcpListener::bind(addr).await
            .map_err(|e| FapError::network(format!("Failed to bind to {}: {}", addr, e)))?;
        
        info!("🚀 {} server starting on http://{}", self.name, addr);
        
        serve(listener, self.router).await
            .map_err(|e| FapError::network(format!("Server error: {}", e)))?;
        
        Ok(())
    }
    
    /// Get the router for testing or advanced usage
    pub fn into_router(self) -> Router {
        self.router
    }
}

/// Default health check handler
async fn default_health_handler() -> axum::Json<ApiResponse<serde_json::Value>> {
    axum::Json(ApiResponse::success(serde_json::json!({
        "status": "healthy",
        "timestamp": FapTime::now().to_rfc3339(),
        "version": fap_core::VERSION
    })))
}
