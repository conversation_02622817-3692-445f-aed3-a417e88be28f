use axum::{http::StatusCode, response::IntoResponse, Json};
use fap_core::prelude::*;

/// Standard API response wrapper for FAP applications
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub timestamp: String,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            timestamp: FapTime::now().to_rfc3339(),
        }
    }
    
    pub fn error(message: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message),
            timestamp: FapTime::now().to_rfc3339(),
        }
    }
}

/// Enhanced response type with status codes
pub struct FapResponse<T> {
    pub status: StatusCode,
    pub body: ApiResponse<T>,
}

impl<T> FapResponse<T> {
    pub fn ok(data: T) -> Self {
        Self {
            status: StatusCode::OK,
            body: ApiResponse::success(data),
        }
    }
    
    pub fn created(data: T) -> Self {
        Self {
            status: StatusCode::CREATED,
            body: ApiResponse::success(data),
        }
    }
    
    pub fn bad_request(message: String) -> Self {
        Self {
            status: StatusCode::BAD_REQUEST,
            body: ApiResponse::error(message),
        }
    }
    
    pub fn not_found(message: String) -> Self {
        Self {
            status: StatusCode::NOT_FOUND,
            body: ApiResponse::error(message),
        }
    }
    
    pub fn internal_error(message: String) -> Self {
        Self {
            status: StatusCode::INTERNAL_SERVER_ERROR,
            body: ApiResponse::error(message),
        }
    }
}

impl<T> IntoResponse for FapResponse<T>
where
    T: Serialize,
{
    fn into_response(self) -> axum::response::Response {
        (self.status, Json(self.body)).into_response()
    }
}
