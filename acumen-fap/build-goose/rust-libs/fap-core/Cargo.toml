[package]
name = "fap-core"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Core utilities and types for FAP applications"

[dependencies]
# Essential dependencies used by all FAP Rust apps
serde.workspace = true
serde_json.workspace = true
anyhow.workspace = true
thiserror.workspace = true
uuid.workspace = true
chrono.workspace = true
tracing.workspace = true

[features]
default = []
async = ["tokio"]
config = ["serde_yaml", "etcetera", "dirs"]

[dependencies.tokio]
workspace = true
optional = true

[dependencies.serde_yaml]
workspace = true
optional = true

[dependencies.etcetera]
workspace = true
optional = true

[dependencies.dirs]
workspace = true
optional = true
