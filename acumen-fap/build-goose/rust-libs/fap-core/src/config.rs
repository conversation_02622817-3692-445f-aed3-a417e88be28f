use crate::error::{FapError, FapResult};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::path::PathBuf;

/// Standard configuration trait for FAP applications
pub trait FapConfig: Serialize + for<'de> Deserialize<'de> + Default {
    /// Get the configuration file name (e.g., "app-name.yaml")
    fn config_filename() -> &'static str;
    
    /// Load configuration from the standard FAP config directory
    fn load() -> FapResult<Self> {
        let config_dir = dirs::config_dir()
            .ok_or_else(|| FapError::config("Failed to get config directory".to_string()))?
            .join("fap");
        
        let config_path = config_dir.join(Self::config_filename());
        
        if !config_path.exists() {
            // Create default config if it doesn't exist
            let default_config = Self::default();
            default_config.save()?;
            return Ok(default_config);
        }
        
        let content = std::fs::read_to_string(&config_path)
            .map_err(|e| FapError::config(format!("Failed to read config file: {}", e)))?;
        
        serde_yaml::from_str(&content)
            .map_err(|e| FapError::config(format!("Failed to parse config: {}", e)))
    }
    
    /// Save configuration to the standard FAP config directory
    fn save(&self) -> FapResult<()> {
        let config_dir = dirs::config_dir()
            .ok_or_else(|| FapError::config("Failed to get config directory".to_string()))?
            .join("fap");
        
        std::fs::create_dir_all(&config_dir)
            .map_err(|e| FapError::config(format!("Failed to create config directory: {}", e)))?;
        
        let config_path = config_dir.join(Self::config_filename());
        let content = serde_yaml::to_string(self)
            .map_err(|e| FapError::config(format!("Failed to serialize config: {}", e)))?;
        
        std::fs::write(&config_path, content)
            .map_err(|e| FapError::config(format!("Failed to write config file: {}", e)))?;
        
        Ok(())
    }
    
    /// Get the path to the configuration file
    fn config_path() -> FapResult<PathBuf> {
        let config_dir = dirs::config_dir()
            .ok_or_else(|| FapError::config("Failed to get config directory".to_string()))?
            .join("fap");
        
        Ok(config_dir.join(Self::config_filename()))
    }
}

/// Simple configuration manager for JSON-based configs
#[derive(Debug, Clone)]
pub struct ConfigManager {
    config_dir: PathBuf,
}

impl ConfigManager {
    /// Create a new config manager
    pub fn new() -> FapResult<Self> {
        let config_dir = dirs::config_dir()
            .ok_or_else(|| FapError::config("Failed to get config directory".to_string()))?
            .join("fap");
        
        std::fs::create_dir_all(&config_dir)
            .map_err(|e| FapError::config(format!("Failed to create config directory: {}", e)))?;
        
        Ok(Self { config_dir })
    }
    
    /// Check if any configuration exists
    pub fn exists(&self) -> bool {
        self.config_dir.exists() && self.config_dir.read_dir().map_or(false, |mut entries| entries.next().is_some())
    }
    
    /// Load configuration by name
    pub fn load(&self, name: &str) -> FapResult<Value> {
        let config_path = self.config_dir.join(format!("{}.json", name));
        
        if !config_path.exists() {
            return Err(FapError::config(format!("Configuration '{}' not found", name)));
        }
        
        let content = std::fs::read_to_string(&config_path)
            .map_err(|e| FapError::config(format!("Failed to read config file: {}", e)))?;
        
        serde_json::from_str(&content)
            .map_err(|e| FapError::config(format!("Failed to parse config: {}", e)))
    }
    
    /// Save configuration by name
    pub fn save(&self, name: &str, config: &Value) -> FapResult<()> {
        let config_path = self.config_dir.join(format!("{}.json", name));
        
        let content = serde_json::to_string_pretty(config)
            .map_err(|e| FapError::config(format!("Failed to serialize config: {}", e)))?;
        
        std::fs::write(&config_path, content)
            .map_err(|e| FapError::config(format!("Failed to write config file: {}", e)))?;
        
        Ok(())
    }
    
    /// Clear all configuration
    pub fn clear(&self) -> FapResult<()> {
        if self.config_dir.exists() {
            std::fs::remove_dir_all(&self.config_dir)
                .map_err(|e| FapError::config(format!("Failed to clear config directory: {}", e)))?;
            
            std::fs::create_dir_all(&self.config_dir)
                .map_err(|e| FapError::config(format!("Failed to recreate config directory: {}", e)))?;
        }
        Ok(())
    }
    
    /// List all configuration files
    pub fn list(&self) -> FapResult<Vec<String>> {
        let mut configs = Vec::new();
        
        if !self.config_dir.exists() {
            return Ok(configs);
        }
        
        for entry in std::fs::read_dir(&self.config_dir)
            .map_err(|e| FapError::config(format!("Failed to read config directory: {}", e)))? {
            let entry = entry
                .map_err(|e| FapError::config(format!("Failed to read directory entry: {}", e)))?;
            
            if let Some(name) = entry.path().file_stem().and_then(|s| s.to_str()) {
                if entry.path().extension().and_then(|s| s.to_str()) == Some("json") {
                    configs.push(name.to_string());
                }
            }
        }
        
        configs.sort();
        Ok(configs)
    }
}

/// Example configuration structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExampleConfig {
    pub app_name: String,
    pub port: u16,
    pub debug: bool,
}

impl Default for ExampleConfig {
    fn default() -> Self {
        Self {
            app_name: "fap-app".to_string(),
            port: 3000,
            debug: false,
        }
    }
}

impl FapConfig for ExampleConfig {
    fn config_filename() -> &'static str {
        "example.yaml"
    }
}
