/*!
# FAP Core Library

Core utilities and types shared across all FAP Rust applications.
This library provides the foundation for fast, consistent builds across the monorepo.

## Features

- `async`: Enables async runtime utilities (tokio)
- `config`: Enables configuration management utilities

## Usage

```rust
use fap_core::prelude::*;

// Common types and utilities are available
let id = FapId::new();
let result = FapResult::Ok("success".to_string());
```
*/

// Start with only the essential, working modules
pub mod error;
pub mod time;      // Added back - should compile fine
pub mod id;        // Added back - UUID serde issue should be fixed

#[cfg(feature = "async")]
pub mod async_utils; // Added back with feature flag

#[cfg(feature = "config")]
pub mod config;    // Added back - etcetera API issues should be fixed

/// Minimal prelude for FAP applications
pub mod prelude {
    pub use crate::error::{FapError, FapResult};
    pub use crate::time::FapTime;
    pub use crate::id::FapId;
    
    // Re-export only essential external types
    pub use anyhow::{anyhow, bail, Context, Result as AnyhowResult};
    pub use serde::{Deserialize, Serialize};
    pub use serde_json::{json, Value as JsonValue};
    pub use uuid::Uuid;
    pub use chrono::{DateTime, Utc};
    pub use tracing::{debug, error, info, trace, warn};
    
    #[cfg(feature = "async")]
    pub use tokio;
    
    #[cfg(feature = "async")]
    pub use crate::async_utils::AsyncUtils;
    
    #[cfg(feature = "config")]
    pub use crate::config::FapConfig;
}

// Version information
pub const VERSION: &str = env!("CARGO_PKG_VERSION");
pub const NAME: &str = env!("CARGO_PKG_NAME");
