use serde::{Deserialize, Serialize};
use std::fmt;
use uuid::Uuid;

/// Standard ID type for FAP applications
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct FapId(Uuid);

impl FapId {
    /// Create a new random FAP ID
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }
    
    /// Create a FAP ID from a UUID
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }
    
    /// Create a FAP ID from a string
    pub fn from_string(s: &str) -> Result<Self, uuid::Error> {
        Ok(Self(Uuid::parse_str(s)?))
    }
    
    /// Get the underlying UUID
    pub fn as_uuid(&self) -> &Uuid {
        &self.0
    }
    
    /// Convert to string
    pub fn to_string(&self) -> String {
        self.0.to_string()
    }
    
    /// Get a short representation (first 8 characters)
    pub fn short(&self) -> String {
        self.0.to_string()[..8].to_string()
    }
}

impl Default for FapId {
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for FapId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

impl From<Uuid> for FapId {
    fn from(uuid: Uuid) -> Self {
        Self(uuid)
    }
}

impl From<FapId> for Uuid {
    fn from(id: FapId) -> Self {
        id.0
    }
}

/// Generate a new random ID as a string
pub fn generate_id() -> String {
    FapId::new().to_string()
}
