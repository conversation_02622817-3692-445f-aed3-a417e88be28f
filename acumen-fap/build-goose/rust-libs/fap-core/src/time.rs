use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// Standard timestamp type for FAP applications
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub struct FapTime(DateTime<Utc>);

impl FapTime {
    /// Create a new timestamp with the current time
    pub fn now() -> Self {
        Self(Utc::now())
    }
    
    /// Create from a DateTime<Utc>
    pub fn from_datetime(dt: DateTime<Utc>) -> Self {
        Self(dt)
    }
    
    /// Create from RFC3339 string
    pub fn from_rfc3339(s: &str) -> Result<Self, chrono::ParseError> {
        Ok(Self(DateTime::parse_from_rfc3339(s)?.with_timezone(&Utc)))
    }
    
    /// Convert to RFC3339 string
    pub fn to_rfc3339(&self) -> String {
        self.0.to_rfc3339()
    }
    
    /// Get the underlying DateTime
    pub fn as_datetime(&self) -> &DateTime<Utc> {
        &self.0
    }
    
    /// Format for display
    pub fn format(&self, fmt: &str) -> String {
        self.0.format(fmt).to_string()
    }
    
    /// Get a human-readable relative time (e.g., "2 minutes ago")
    pub fn relative(&self) -> String {
        let now = Utc::now();
        let duration = now.signed_duration_since(self.0);
        
        if duration.num_seconds() < 60 {
            "just now".to_string()
        } else if duration.num_minutes() < 60 {
            format!("{} minutes ago", duration.num_minutes())
        } else if duration.num_hours() < 24 {
            format!("{} hours ago", duration.num_hours())
        } else {
            format!("{} days ago", duration.num_days())
        }
    }
}

impl Default for FapTime {
    fn default() -> Self {
        Self::now()
    }
}

impl From<DateTime<Utc>> for FapTime {
    fn from(dt: DateTime<Utc>) -> Self {
        Self(dt)
    }
}

impl From<FapTime> for DateTime<Utc> {
    fn from(time: FapTime) -> Self {
        time.0
    }
}

/// Get the current timestamp as an ISO 8601 string
pub fn current_timestamp() -> String {
    FapTime::now().to_rfc3339()
}
