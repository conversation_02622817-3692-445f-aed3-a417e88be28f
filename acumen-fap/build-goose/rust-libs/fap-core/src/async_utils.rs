use crate::error::{FapError, FapResult};
use tokio::time::{timeout, Duration};

/// Utility functions for async operations
pub struct AsyncUtils;

impl AsyncUtils {
    /// Run a future with a timeout
    pub async fn with_timeout<F, T>(
        future: F,
        timeout_duration: Duration,
    ) -> FapResult<T>
    where
        F: std::future::Future<Output = T>,
    {
        timeout(timeout_duration, future)
            .await
            .map_err(|_| FapError::generic("Operation timed out"))
    }
    
    /// Retry an async operation with exponential backoff
    pub async fn retry_with_backoff<F, Fut, T>(
        mut operation: F,
        max_retries: usize,
        initial_delay: Duration,
    ) -> FapResult<T>
    where
        F: FnMut() -> Fut,
        Fut: std::future::Future<Output = FapResult<T>>,
    {
        let mut delay = initial_delay;
        
        for attempt in 0..=max_retries {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(e) if attempt == max_retries => return Err(e),
                Err(_) => {
                    tokio::time::sleep(delay).await;
                    delay *= 2; // Exponential backoff
                }
            }
        }
        
        unreachable!()
    }
    
    /// Spawn a background task with error handling
    pub fn spawn_background<F>(future: F) -> tokio::task::JoinHandle<()>
    where
        F: std::future::Future<Output = FapResult<()>> + Send + 'static,
    {
        tokio::spawn(async move {
            if let Err(e) = future.await {
                tracing::error!("Background task failed: {}", e);
            }
        })
    }
}
