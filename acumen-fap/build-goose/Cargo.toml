[workspace]
resolver = "2"
members = [
    "rust-libs/fap-core",
    "rust-libs/fap-web",
    "apps/goose-fap",
    "apps/goose-fap-cli",
    "apps/tauri-bootstrapper",
    # Add future Rust apps here
]

# Shared dependency versions across the entire monorepo
[workspace.dependencies]
# Core async runtime (used by almost everything)
tokio = { version = "1.43", features = ["rt-multi-thread", "macros", "net", "fs", "time"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
thiserror = "1.0"
futures = "0.3"
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# Web server stack (for web apps)
axum = { version = "0.8.1", features = ["ws", "macros"] }
tower-http = { version = "0.5", features = ["cors", "fs", "trace"] }
tower = "0.4"
http = "1.0"
hyper = "1.0"

# HTTP client
reqwest = { version = "0.12", features = ["json", "rustls-tls-native-roots"] }

# Logging and tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "fmt"] }

# CLI utilities
clap = { version = "4.4", features = ["derive"] }
console = "0.15"
dialoguer = "0.11"
indicatif = "0.17"

# Configuration and filesystem
serde_yaml = "0.9"
etcetera = "0.8.0"
dirs = "5.0"

# Tauri dependencies (for desktop apps)
tauri = { version = "2.0", features = ["protocol-asset"] }
tauri-build = "2.0"

# AI and vector databases
lancedb = "0.13"
arrow = "52.2"

# P2P networking (for Pear integration)
libp2p = { version = "0.54", features = ["tcp", "mdns", "noise", "yamux", "gossipsub"] }

# Crypto
sha2 = "0.10"
rand = "0.8"

# Database connections
rusqlite = { version = "0.32", features = ["bundled"] }

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["Acumen Desktop Software Canada Inc."]
license = "Apache-2.0"
repository = "https://github.com/acumen-desktop/fap-monorepo"
description = "FAP Monorepo - Acumen Desktop Software Canada Inc."

# Optimize for fast builds and small binaries
[profile.dev]
opt-level = 0
debug = true
incremental = true

[profile.release]
opt-level = 3
lto = "thin"  # Faster than "fat" but still optimized
codegen-units = 1
panic = "abort"
strip = true

# Fast development profile
[profile.dev-fast]
inherits = "dev"
opt-level = 1
debug = false
incremental = true
