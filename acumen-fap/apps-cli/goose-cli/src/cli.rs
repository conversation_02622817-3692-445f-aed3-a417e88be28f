use anyhow::Result;
use clap::{Parser, Subcommand};
use std::path::PathBuf;

use crate::commands;

#[derive(Parser)]
#[command(
    name = "goose-fap",
    version = "0.1.0",
    about = "Goose AI CLI replica using FAP shared libraries",
    long_about = "A lightweight CLI replica of Goose AI agent functionality built with FAP shared Rust libraries"
)]
pub struct Cli {
    #[command(subcommand)]
    pub command: Option<Command>,
}

#[derive(Subcommand)]
pub enum Command {
    /// Configure Goose settings
    #[command(about = "Configure Goose settings")]
    Configure {
        /// Reset configuration to defaults
        #[arg(long, help = "Reset configuration to defaults")]
        reset: bool,
    },

    /// Display Goose configuration information
    #[command(about = "Display Goose information")]
    Info {
        /// Show verbose information
        #[arg(short, long, help = "Show verbose information")]
        verbose: bool,
    },

    /// Start or resume interactive chat sessions
    #[command(
        about = "Start or resume interactive chat sessions",
        visible_alias = "s"
    )]
    Session {
        #[command(subcommand)]
        command: Option<SessionCommand>,

        /// Session name or identifier
        #[arg(short, long, help = "Name for the chat session")]
        name: Option<String>,

        /// Resume a previous session
        #[arg(short, long, help = "Resume a previous session")]
        resume: bool,

        /// Session file path
        #[arg(short, long, help = "Path for the chat session")]
        path: Option<PathBuf>,

        /// Enable debug mode
        #[arg(long, help = "Enable debug mode with verbose output")]
        debug: bool,
    },

    /// Start web interface
    #[command(about = "Start web interface")]
    Web {
        /// Port to run on
        #[arg(short, long, default_value = "3001", help = "Port to run web interface on")]
        port: u16,

        /// Host to bind to
        #[arg(long, default_value = "127.0.0.1", help = "Host to bind to")]
        host: String,

        /// Open browser automatically
        #[arg(long, help = "Open browser automatically")]
        open: bool,
    },
}

#[derive(Subcommand)]
pub enum SessionCommand {
    /// List all available sessions
    #[command(about = "List all available sessions")]
    List {
        /// Show verbose information
        #[arg(short, long, help = "Show verbose session information")]
        verbose: bool,

        /// Output format (text, json)
        #[arg(short, long, default_value = "text", help = "Output format")]
        format: String,

        /// Sort in ascending order
        #[arg(long, help = "Sort by date in ascending order")]
        ascending: bool,
    },

    /// Remove sessions
    #[command(about = "Remove sessions")]
    Remove {
        /// Session ID to remove
        #[arg(short, long, help = "Session ID to remove")]
        id: Option<String>,

        /// Regex pattern for bulk removal
        #[arg(short, long, help = "Regex pattern for bulk removal")]
        regex: Option<String>,
    },

    /// Export session to Markdown
    #[command(about = "Export session to Markdown")]
    Export {
        /// Session name or ID
        #[arg(short, long, help = "Session name or ID to export")]
        name: Option<String>,

        /// Output file path
        #[arg(short, long, help = "Output file path (default: stdout)")]
        output: Option<PathBuf>,
    },
}

impl Cli {
    pub async fn execute(self) -> Result<()> {
        match self.command {
            Some(Command::Configure { reset }) => {
                commands::configure::handle_configure(reset).await
            }
            Some(Command::Info { verbose }) => {
                commands::info::handle_info(verbose).await
            }
            Some(Command::Session {
                command,
                name,
                resume,
                path,
                debug,
            }) => {
                commands::session::handle_session(command, name, resume, path, debug).await
            }
            Some(Command::Web { port, host, open }) => {
                commands::web::handle_web(port, host, open).await
            }
            None => {
                // Default behavior: start interactive session
                commands::session::handle_session(None, None, false, None, false).await
            }
        }
    }
}
