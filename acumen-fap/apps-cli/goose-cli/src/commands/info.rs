use anyhow::Result;
use console::style;
use fap_core::config::ConfigManager;
use serde_json::Value;
use std::env;
use tracing::info;

pub async fn handle_info(verbose: bool) -> Result<()> {
    println!();
    println!("{}", style("🪿 Goose FAP Information").cyan().bold());
    println!();

    // Version information
    println!("{}", style("📦 Version Information").green().bold());
    println!("  Goose FAP CLI: {}", env!("CARGO_PKG_VERSION"));
    println!("  Built with: Rust {}", std::env::var("RUSTC_VERSION").unwrap_or_else(|_| "unknown".to_string()));
    println!();

    // Configuration information
    let config_manager = ConfigManager::new()?;
    
    if config_manager.exists() {
        println!("{}", style("⚙️  Configuration").green().bold());
        
        match config_manager.load("goose") {
            Ok(config) => {
                if let Value::Object(ref map) = config {
                    // Show basic configuration
                    if let Some(Value::String(provider)) = map.get("provider") {
                        println!("  Provider: {}", style(provider).cyan());
                    }
                    
                    if let Some(Value::String(model)) = map.get("model") {
                        println!("  Model: {}", style(model).cyan());
                    }
                    
                    if let Some(Value::String(endpoint)) = map.get("endpoint") {
                        println!("  Endpoint: {}", style(endpoint).cyan());
                    }
                    
                    if let Some(Value::Number(max_tokens)) = map.get("max_tokens") {
                        println!("  Max Tokens: {}", style(max_tokens).cyan());
                    }
                    
                    if let Some(Value::Number(temperature)) = map.get("temperature") {
                        println!("  Temperature: {}", style(temperature).cyan());
                    }
                    
                    // Show sensitive information only in verbose mode
                    if verbose {
                        println!();
                        println!("{}", style("🔐 Sensitive Information").yellow().bold());
                        if let Some(Value::String(api_key)) = map.get("api_key") {
                            let masked_key = if api_key.len() > 8 {
                                format!("{}...{}", &api_key[..4], &api_key[api_key.len()-4..])
                            } else {
                                "***".to_string()
                            };
                            println!("  API Key: {}", style(masked_key).dim());
                        }
                    }
                    
                    if verbose {
                        println!();
                        println!("{}", style("📋 Full Configuration").dim());
                        println!("{}", serde_json::to_string_pretty(&config)?);
                    }
                } else {
                    println!("  {}", style("Invalid configuration format").red());
                }
            }
            Err(e) => {
                println!("  {}: {}", style("Error loading configuration").red(), e);
            }
        }
    } else {
        println!("{}", style("⚠️  No Configuration Found").yellow().bold());
        println!("  Run 'goose-fap configure' to set up your AI provider");
    }
    
    println!();

    // System information
    if verbose {
        println!("{}", style("💻 System Information").green().bold());
        println!("  OS: {}", env::consts::OS);
        println!("  Architecture: {}", env::consts::ARCH);
        
        if let Ok(current_dir) = env::current_dir() {
            println!("  Working Directory: {}", current_dir.display());
        }
        
        // Environment variables
        println!();
        println!("{}", style("🌍 Environment").dim());
        let env_vars = ["RUST_LOG", "GOOSE_CONFIG_PATH", "OPENAI_API_KEY", "ANTHROPIC_API_KEY"];
        for var in &env_vars {
            match env::var(var) {
                Ok(value) => {
                    let display_value = if var.contains("KEY") && !value.is_empty() {
                        "***set***".to_string()
                    } else {
                        value
                    };
                    println!("  {}: {}", var, style(display_value).cyan());
                }
                Err(_) => {
                    println!("  {}: {}", var, style("not set").dim());
                }
            }
        }
        println!();
    }

    // Usage tips
    println!("{}", style("💡 Quick Start").cyan().bold());
    println!("  • Configure: goose-fap configure");
    println!("  • Start session: goose-fap session");
    println!("  • List sessions: goose-fap session list");
    println!("  • Web interface: goose-fap web");
    println!("  • Help: goose-fap --help");
    println!();

    info!("Info command completed (verbose: {})", verbose);
    Ok(())
}
