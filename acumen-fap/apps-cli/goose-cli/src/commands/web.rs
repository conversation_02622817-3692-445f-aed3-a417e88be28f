use anyhow::Result;
use console::style;
use fap_web::{response::ApiResponse, handlers::health_check};
use std::process::Command;
use tokio::net::TcpListener;
use tracing::{info, warn};

pub async fn handle_web(port: u16, host: String, open: bool) -> Result<()> {
    println!();
    println!("{}", style("🌐 Starting Goose FAP Web Interface").cyan().bold());
    println!();

    let addr = format!("{}:{}", host, port);
    
    // Check if port is available
    match TcpListener::bind(&addr).await {
        Ok(_) => {
            println!("{} Server will start on: {}", 
                style("🚀").green(), 
                style(format!("http://{}", addr)).cyan().underlined()
            );
        }
        Err(e) => {
            println!("{} Port {} is already in use: {}", 
                style("❌").red(), port, e);
            return Err(anyhow::anyhow!("Port {} is not available", port));
        }
    }

    if open {
        println!("{}", style("🔗 Opening browser...").dim());
        let url = format!("http://{}", addr);
        
        // Try to open browser (cross-platform)
        let result = if cfg!(target_os = "macos") {
            Command::new("open").arg(&url).spawn()
        } else if cfg!(target_os = "windows") {
            Command::new("cmd").args(["/c", "start", &url]).spawn()
        } else {
            Command::new("xdg-open").arg(&url).spawn()
        };

        match result {
            Ok(_) => info!("Browser opened for {}", url),
            Err(e) => warn!("Failed to open browser: {}", e),
        }
    }

    println!();
    println!("{}", style("💡 Web Interface Features:").cyan());
    println!("  • Interactive chat interface");
    println!("  • Session management");
    println!("  • Configuration panel");
    println!("  • Real-time AI responses");
    println!();
    println!("{}", style("Press Ctrl+C to stop the server").dim());
    println!();

    // Start the web server using fap-web
    start_web_server(host, port).await
}

async fn start_web_server(host: String, port: u16) -> Result<()> {
    use tokio::io::{AsyncReadExt, AsyncWriteExt};
    use tokio::net::TcpListener;

    let addr = format!("{}:{}", host, port);
    let listener = TcpListener::bind(&addr).await?;
    
    info!("Web server listening on {}", addr);
    println!("{} Web server started successfully", style("✅").green());

    loop {
        match listener.accept().await {
            Ok((mut stream, peer_addr)) => {
                info!("New connection from {}", peer_addr);
                
                tokio::spawn(async move {
                    let mut buffer = [0; 1024];
                    
                    match stream.read(&mut buffer).await {
                        Ok(bytes_read) => {
                            let request = String::from_utf8_lossy(&buffer[..bytes_read]);
                            info!("Request: {}", request.lines().next().unwrap_or(""));
                            
                            // Parse request path
                            let path = extract_path_from_request(&request);
                            
                            let response = match path.as_str() {
                                "/" => serve_index_page(),
                                "/health" => {
                                    let health = health_check().await;
                                    match health {
                                        Ok(response) => format_json_response(&response),
                                        Err(e) => format_error_response(&format!("Health check failed: {}", e)),
                                    }
                                }
                                "/api/config" => serve_config_api(),
                                "/api/sessions" => serve_sessions_api(),
                                _ => serve_404_page(),
                            };
                            
                            if let Err(e) = stream.write_all(response.as_bytes()).await {
                                warn!("Failed to write response: {}", e);
                            }
                        }
                        Err(e) => {
                            warn!("Failed to read from stream: {}", e);
                        }
                    }
                });
            }
            Err(e) => {
                warn!("Failed to accept connection: {}", e);
            }
        }
    }
}

fn extract_path_from_request(request: &str) -> String {
    request
        .lines()
        .next()
        .and_then(|line| line.split_whitespace().nth(1))
        .unwrap_or("/")
        .split('?')
        .next()
        .unwrap_or("/")
        .to_string()
}

fn serve_index_page() -> String {
    let html = r#"<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Goose FAP - AI Assistant</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            width: 90%;
            text-align: center;
        }
        .logo { font-size: 3rem; margin-bottom: 1rem; }
        h1 { color: #333; margin-bottom: 0.5rem; font-size: 2rem; }
        .subtitle { color: #666; margin-bottom: 2rem; }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        .feature {
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .feature-icon { font-size: 1.5rem; margin-bottom: 0.5rem; }
        .status { 
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            margin-top: 1rem;
        }
        .links { margin-top: 2rem; }
        .link {
            display: inline-block;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.2s;
        }
        .link:hover { background: #5a67d8; }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🪿</div>
        <h1>Goose FAP</h1>
        <p class="subtitle">AI Assistant powered by FAP shared libraries</p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">💬</div>
                <div>Interactive Chat</div>
            </div>
            <div class="feature">
                <div class="feature-icon">📋</div>
                <div>Session Management</div>
            </div>
            <div class="feature">
                <div class="feature-icon">⚙️</div>
                <div>Configuration</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🔧</div>
                <div>Debug Tools</div>
            </div>
        </div>
        
        <div class="status">✅ Server Running</div>
        
        <div class="links">
            <a href="/health" class="link">Health Check</a>
            <a href="/api/config" class="link">Configuration</a>
            <a href="/api/sessions" class="link">Sessions</a>
        </div>
        
        <p style="margin-top: 2rem; color: #666; font-size: 0.875rem;">
            Built with FAP shared Rust libraries<br>
            Use the CLI for full functionality: <code>goose-fap --help</code>
        </p>
    </div>
</body>
</html>"#;

    format!(
        "HTTP/1.1 200 OK\r\nContent-Type: text/html\r\nContent-Length: {}\r\n\r\n{}",
        html.len(),
        html
    )
}

fn serve_config_api() -> String {
    let config_data = serde_json::json!({
        "status": "success",
        "message": "Configuration API endpoint",
        "data": {
            "version": env!("CARGO_PKG_VERSION"),
            "features": ["cli", "web", "sessions", "config"]
        }
    });
    
    format_json_response(&ApiResponse::success(config_data))
}

fn serve_sessions_api() -> String {
    let sessions_data = serde_json::json!({
        "status": "success", 
        "message": "Sessions API endpoint",
        "data": {
            "sessions": [],
            "total": 0
        }
    });
    
    format_json_response(&ApiResponse::success(sessions_data))
}

fn serve_404_page() -> String {
    let html = r#"<!DOCTYPE html>
<html><head><title>404 Not Found</title></head>
<body style="font-family: sans-serif; text-align: center; padding: 2rem;">
<h1>404 - Not Found</h1>
<p>The requested resource was not found.</p>
<a href="/">← Back to Home</a>
</body></html>"#;

    format!(
        "HTTP/1.1 404 Not Found\r\nContent-Type: text/html\r\nContent-Length: {}\r\n\r\n{}",
        html.len(),
        html
    )
}

fn format_json_response<T: serde::Serialize>(data: &ApiResponse<T>) -> String {
    let json = serde_json::to_string_pretty(data).unwrap_or_else(|_| "{}".to_string());
    format!(
        "HTTP/1.1 200 OK\r\nContent-Type: application/json\r\nContent-Length: {}\r\n\r\n{}",
        json.len(),
        json
    )
}

fn format_error_response(error: &str) -> String {
    let json = serde_json::json!({
        "status": "error",
        "message": error
    });
    let json_str = serde_json::to_string(&json).unwrap_or_else(|_| "{}".to_string());
    format!(
        "HTTP/1.1 500 Internal Server Error\r\nContent-Type: application/json\r\nContent-Length: {}\r\n\r\n{}",
        json_str.len(),
        json_str
    )
}
