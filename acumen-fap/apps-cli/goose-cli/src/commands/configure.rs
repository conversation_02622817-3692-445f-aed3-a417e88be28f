use anyhow::Result;
use console::style;
use dialoguer::{Confirm, Input, Select};
use fap_core::config::ConfigManager;
use serde_json::Value;
use std::collections::HashMap;
use tracing::info;

pub async fn handle_configure(reset: bool) -> Result<()> {
    println!();
    println!("{}", style("🪿 Goose FAP Configuration").cyan().bold());
    println!("{}", style("Configure your AI provider and settings").dim());
    println!();

    let config_manager = ConfigManager::new()?;

    if reset {
        if Confirm::new()
            .with_prompt("Are you sure you want to reset all configuration?")
            .interact()?
        {
            config_manager.clear()?;
            println!("{}", style("✅ Configuration reset successfully").green());
            return Ok(());
        } else {
            println!("{}", style("Configuration reset cancelled").yellow());
            return Ok(());
        }
    }

    // Check if configuration exists
    if config_manager.exists() {
        println!("{}", style("📋 Current configuration found").green());
        
        let update = Confirm::new()
            .with_prompt("Would you like to update your existing configuration?")
            .interact()?;
            
        if !update {
            println!("{}", style("Configuration unchanged").dim());
            return Ok(());
        }
    } else {
        println!("{}", style("🚀 First time setup").cyan());
    }

    // Provider selection
    let providers = vec![
        ("openai", "OpenAI (GPT-4, GPT-3.5)"),
        ("anthropic", "Anthropic (Claude)"),
        ("openrouter", "OpenRouter (Multiple providers)"),
        ("local", "Local/Self-hosted"),
    ];

    let provider_selection = Select::new()
        .with_prompt("Select your AI provider")
        .items(&providers.iter().map(|(_, desc)| *desc).collect::<Vec<_>>())
        .interact()?;

    let (provider_key, provider_name) = providers[provider_selection];
    
    println!();
    println!("{} {}", style("Configuring").dim(), style(provider_name).cyan());

    let mut config = HashMap::new();
    config.insert("provider".to_string(), Value::String(provider_key.to_string()));

    // Get API key or endpoint based on provider
    match provider_key {
        "openai" => {
            let api_key: String = Input::new()
                .with_prompt("Enter your OpenAI API key")
                .interact_text()?;
            config.insert("api_key".to_string(), Value::String(api_key));
            
            let model: String = Input::new()
                .with_prompt("Enter model name")
                .with_initial_text("gpt-4")
                .interact_text()?;
            config.insert("model".to_string(), Value::String(model));
        }
        "anthropic" => {
            let api_key: String = Input::new()
                .with_prompt("Enter your Anthropic API key")
                .interact_text()?;
            config.insert("api_key".to_string(), Value::String(api_key));
            
            let model: String = Input::new()
                .with_prompt("Enter model name")
                .with_initial_text("claude-3-sonnet-20240229")
                .interact_text()?;
            config.insert("model".to_string(), Value::String(model));
        }
        "openrouter" => {
            let api_key: String = Input::new()
                .with_prompt("Enter your OpenRouter API key")
                .interact_text()?;
            config.insert("api_key".to_string(), Value::String(api_key));
            
            let model: String = Input::new()
                .with_prompt("Enter model name")
                .with_initial_text("anthropic/claude-3-sonnet")
                .interact_text()?;
            config.insert("model".to_string(), Value::String(model));
        }
        "local" => {
            let endpoint: String = Input::new()
                .with_prompt("Enter your local endpoint URL")
                .with_initial_text("http://localhost:11434")
                .interact_text()?;
            config.insert("endpoint".to_string(), Value::String(endpoint));
            
            let model: String = Input::new()
                .with_prompt("Enter model name")
                .with_initial_text("llama2")
                .interact_text()?;
            config.insert("model".to_string(), Value::String(model));
        }
        _ => unreachable!(),
    }

    // Additional settings
    let max_tokens: String = Input::new()
        .with_prompt("Maximum tokens per response")
        .with_initial_text("4096")
        .interact_text()?;
    
    if let Ok(tokens) = max_tokens.parse::<u32>() {
        config.insert("max_tokens".to_string(), Value::Number(tokens.into()));
    }

    let temperature: String = Input::new()
        .with_prompt("Temperature (0.0-1.0)")
        .with_initial_text("0.7")
        .interact_text()?;
    
    if let Ok(temp) = temperature.parse::<f64>() {
        config.insert("temperature".to_string(), Value::Number(serde_json::Number::from_f64(temp).unwrap()));
    }

    // Save configuration
    let config_value = Value::Object(config.into_iter().collect());
    config_manager.save("goose", &config_value)?;

    println!();
    println!("{}", style("✅ Configuration saved successfully!").green().bold());
    println!();
    println!("{}", style("💡 Tips:").cyan());
    println!("  • Run 'goose-fap info' to view your configuration");
    println!("  • Run 'goose-fap session' to start a chat session");
    println!("  • Run 'goose-fap configure --reset' to reset configuration");
    println!();

    info!("Configuration completed for provider: {}", provider_key);
    Ok(())
}
