use anyhow::Result;
use console::style;
use dialoguer::Confirm;
use fap_core::{config::Config<PERSON>anager, id::generate_id, time::current_timestamp};
use serde_json::{json, Value};
use std::fs;
use std::io::{self, Write};
use std::path::PathBuf;
use tracing::info;

use crate::cli::SessionCommand;

pub async fn handle_session(
    command: Option<SessionCommand>,
    name: Option<String>,
    resume: bool,
    path: Option<PathBuf>,
    debug: bool,
) -> Result<()> {
    match command {
        Some(SessionCommand::List { verbose, format, ascending }) => {
            handle_session_list(verbose, format, ascending).await
        }
        Some(SessionCommand::Remove { id, regex }) => {
            handle_session_remove(id, regex).await
        }
        Some(SessionCommand::Export { name, output }) => {
            handle_session_export(name, output).await
        }
        None => {
            handle_interactive_session(name, resume, path, debug).await
        }
    }
}

async fn handle_session_list(verbose: bool, format: String, ascending: bool) -> Result<()> {
    println!();
    println!("{}", style("📋 Session List").cyan().bold());
    println!();

    let sessions_dir = get_sessions_dir()?;
    if !sessions_dir.exists() {
        println!("{}", style("No sessions found").dim());
        return Ok(());
    }

    let mut sessions = Vec::new();
    
    for entry in fs::read_dir(&sessions_dir)? {
        let entry = entry?;
        let path = entry.path();
        
        if path.extension().and_then(|s| s.to_str()) == Some("jsonl") {
            if let Some(file_name) = path.file_stem().and_then(|s| s.to_str()) {
                let metadata = entry.metadata()?;
                let modified = metadata.modified()?;
                
                sessions.push((file_name.to_string(), path, modified));
            }
        }
    }

    // Sort sessions
    sessions.sort_by(|a, b| {
        if ascending {
            a.2.cmp(&b.2)
        } else {
            b.2.cmp(&a.2)
        }
    });

    match format.as_str() {
        "json" => {
            let json_sessions: Vec<Value> = sessions
                .iter()
                .map(|(name, path, modified)| {
                    json!({
                        "id": name,
                        "path": path.to_string_lossy(),
                        "modified": modified.duration_since(std::time::UNIX_EPOCH)
                            .unwrap_or_default()
                            .as_secs()
                    })
                })
                .collect();
            println!("{}", serde_json::to_string_pretty(&json_sessions)?);
        }
        _ => {
            if sessions.is_empty() {
                println!("{}", style("No sessions found").dim());
            } else {
                println!("Available sessions:");
                for (name, path, modified) in sessions {
                    let time_str = format!("{:?}", modified);
                    if verbose {
                        println!("  {} - {}", style(name).cyan(), style(time_str).dim());
                        println!("    Path: {}", style(path.display()).dim());
                    } else {
                        println!("  {} - {}", style(name).cyan(), style(time_str).dim());
                    }
                }
            }
        }
    }

    println!();
    Ok(())
}

async fn handle_session_remove(id: Option<String>, regex: Option<String>) -> Result<()> {
    println!();
    println!("{}", style("🗑️  Remove Sessions").red().bold());
    println!();

    let sessions_dir = get_sessions_dir()?;
    if !sessions_dir.exists() {
        println!("{}", style("No sessions found").dim());
        return Ok(());
    }

    match (id, regex) {
        (Some(session_id), None) => {
            let session_path = sessions_dir.join(format!("{}.jsonl", session_id));
            if session_path.exists() {
                if Confirm::new()
                    .with_prompt(&format!("Remove session '{}'?", session_id))
                    .interact()?
                {
                    fs::remove_file(&session_path)?;
                    println!("{} Session '{}' removed", style("✅").green(), session_id);
                } else {
                    println!("{}", style("Removal cancelled").yellow());
                }
            } else {
                println!("{} Session '{}' not found", style("❌").red(), session_id);
            }
        }
        (None, Some(_pattern)) => {
            println!("{}", style("Regex removal not yet implemented").yellow());
        }
        (None, None) => {
            println!("{}", style("Interactive removal not yet implemented").yellow());
            println!("Use --id <session_id> to remove a specific session");
        }
        (Some(_), Some(_)) => {
            println!("{}", style("Cannot specify both --id and --regex").red());
        }
    }

    Ok(())
}

async fn handle_session_export(name: Option<String>, _output: Option<PathBuf>) -> Result<()> {
    println!();
    println!("{}", style("📤 Export Session").cyan().bold());
    println!();

    if name.is_none() {
        println!("{}", style("Session export not yet implemented").yellow());
        println!("Use --name <session_name> to specify which session to export");
        return Ok(());
    }

    println!("{}", style("Session export feature coming soon").dim());
    Ok(())
}

async fn handle_interactive_session(
    name: Option<String>,
    resume: bool,
    path: Option<PathBuf>,
    debug: bool,
) -> Result<()> {
    println!();
    println!("{}", style("🪿 Goose FAP Interactive Session").cyan().bold());
    println!();

    // Check configuration
    let config_manager = ConfigManager::new()?;
    if !config_manager.exists() {
        println!("{}", style("⚠️  No configuration found").yellow());
        println!("Run 'goose-fap configure' first to set up your AI provider");
        return Ok(());
    }

    let config = config_manager.load("goose")?;
    if debug {
        println!("{}", style("🔧 Debug mode enabled").dim());
        println!("Configuration: {}", serde_json::to_string_pretty(&config)?);
        println!();
    }

    // Determine session identifier
    let session_id = if let Some(session_name) = name {
        session_name
    } else if let Some(session_path) = path {
        session_path.file_stem()
            .and_then(|s| s.to_str())
            .unwrap_or("unnamed")
            .to_string()
    } else {
        format!("session_{}", generate_id())
    };

    let sessions_dir = get_sessions_dir()?;
    fs::create_dir_all(&sessions_dir)?;
    
    let session_file = sessions_dir.join(format!("{}.jsonl", session_id));

    if resume && session_file.exists() {
        println!("{} Resuming session: {}", style("🔄").cyan(), style(&session_id).bold());
        // Load existing session (simplified for now)
        let content = fs::read_to_string(&session_file)?;
        if !content.trim().is_empty() {
            println!("{}", style("Previous messages:").dim());
            for line in content.lines().take(3) {
                if let Ok(msg) = serde_json::from_str::<Value>(line) {
                    if let Some(content) = msg.get("content").and_then(|c| c.as_str()) {
                        let preview = if content.len() > 80 {
                            format!("{}...", &content[..80])
                        } else {
                            content.to_string()
                        };
                        println!("  {}", style(preview).dim());
                    }
                }
            }
            println!();
        }
    } else {
        println!("{} Starting new session: {}", style("🆕").green(), style(&session_id).bold());
    }

    println!("{}", style("💬 Chat Interface").green());
    println!("Type 'exit' or 'quit' to end the session");
    println!("Type 'help' for available commands");
    println!();

    // Simple chat loop
    loop {
        print!("{} ", style("You:").cyan().bold());
        io::stdout().flush()?;
        
        let mut input = String::new();
        io::stdin().read_line(&mut input)?;
        let input = input.trim();

        if input.is_empty() {
            continue;
        }

        match input.to_lowercase().as_str() {
            "exit" | "quit" => {
                println!("{}", style("👋 Session ended").green());
                break;
            }
            "help" => {
                println!();
                println!("{}", style("Available commands:").cyan());
                println!("  help  - Show this help message");
                println!("  exit  - End the session");
                println!("  quit  - End the session");
                println!();
                continue;
            }
            _ => {
                // Log user message
                let user_msg = json!({
                    "timestamp": current_timestamp(),
                    "role": "user",
                    "content": input
                });
                
                let mut file = fs::OpenOptions::new()
                    .create(true)
                    .append(true)
                    .open(&session_file)?;
                writeln!(file, "{}", serde_json::to_string(&user_msg)?)?;

                // Simulate AI response (placeholder)
                println!("{} {}", style("Goose:").green().bold(), 
                    style("I'm a placeholder response. AI integration coming soon!").dim());
                
                let ai_msg = json!({
                    "timestamp": current_timestamp(),
                    "role": "assistant", 
                    "content": "I'm a placeholder response. AI integration coming soon!"
                });
                
                writeln!(file, "{}", serde_json::to_string(&ai_msg)?)?;
                println!();
            }
        }
    }

    info!("Interactive session '{}' completed", session_id);
    Ok(())
}

fn get_sessions_dir() -> Result<PathBuf> {
    let home_dir = dirs::home_dir()
        .ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
    Ok(home_dir.join(".goose-fap").join("sessions"))
}
