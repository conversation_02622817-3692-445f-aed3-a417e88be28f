[package]
name = "goose-fap-cli"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Goose AI CLI replica using FAP shared libraries"

[[bin]]
name = "goose-fap"
path = "src/main.rs"

[dependencies]
# FAP shared libraries
fap-core = { path = "../../rust-libs/fap-core", features = ["async", "config"] }
fap-web = { path = "../../rust-libs/fap-web" }

# CLI dependencies
clap = { workspace = true, features = ["derive"] }
tokio = { workspace = true, features = ["full"] }
anyhow.workspace = true
serde.workspace = true
serde_json.workspace = true
tracing.workspace = true
tracing-subscriber.workspace = true

# Additional CLI utilities
console = "0.15"
dialoguer = "0.11"
indicatif = "0.17"
dirs.workspace = true
