[package]
name = "tauri-bootstrapper"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Tauri-based bootstrapper for P2P Pear applications"

[dependencies]
# Use our shared FAP libraries (already compiled!)
fap-core = { path = "../../rust-libs/fap-core", features = ["async", "config"] }
fap-web = { path = "../../rust-libs/fap-web", features = ["cors", "static-files"] }

# Tauri dependencies (shared across workspace)
tauri.workspace = true
serde.workspace = true
serde_json.workspace = true
tokio.workspace = true

# P2P networking for Pear integration
libp2p.workspace = true

[build-dependencies]
tauri-build.workspace = true

[features]
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]

[[bin]]
name = "tauri-bootstrapper"
path = "src/main.rs"
