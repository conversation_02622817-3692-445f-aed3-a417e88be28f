# SQLite Databases

Subordinate databases for web serving and application-specific data storage.

## Purpose

SQLite databases serve as fast, local data stores for web applications that need:
- **High-performance reads** for web serving
- **Application-specific schemas** optimized for particular use cases
- **Offline capability** for desktop applications
- **Simple deployment** without server dependencies

## Relationship to TerminusDB

- **TerminusDB**: Single source of truth, master data repository
- **SQLite**: Derived data stores, optimized for specific applications
- **Data Flow**: TerminusDB → Export → SQLite (one-way sync)

## Structure

```
sqlite/
├── web-apps/           # SQLite databases for web applications
│   ├── chat-app.db     # Chat application data
│   ├── client-portal.db # Client portal data
│   └── admin-dashboard.db # Admin interface data
├── mobile-apps/        # SQLite databases for mobile applications
│   ├── field-service.db # Field service app data
│   └── sales-mobile.db  # Mobile sales app data
├── desktop-apps/       # SQLite databases for desktop applications
│   ├── accounting.db    # Desktop accounting app
│   └── project-mgmt.db  # Project management app
└── cache/              # Temporary cache databases
    ├── api-cache.db     # API response caching
    └── search-index.db  # Search index cache
```

## Data Synchronization

### From TerminusDB to SQLite
- **Schedule**: Hourly, daily, or real-time based on application needs
- **Method**: Export from TerminusDB → Transform → Import to SQLite
- **Scripts**: Located in `../terminusdb/scripts/sync/`

### Sync Scripts
- `sync-web-apps.sh` - Sync data for web applications
- `sync-mobile-apps.sh` - Sync data for mobile applications
- `sync-desktop-apps.sh` - Sync data for desktop applications
- `sync-cache.sh` - Update cache databases

## Schema Management

Each SQLite database has:
- **Schema Definition**: `.sql` files defining table structure
- **Migration Scripts**: Version-controlled schema changes
- **Seed Data**: Initial data for new installations
- **Indexes**: Performance optimization for queries

## Performance Optimization

- **Indexes**: Optimized for specific query patterns
- **Denormalization**: Data duplicated for read performance
- **Partitioning**: Large tables split by date or category
- **Caching**: Frequently accessed data cached in memory

## Backup Strategy

- **Included in Git**: Small databases committed for backup
- **Large Databases**: Use Git LFS for databases > 100MB
- **Automated Backups**: Daily exports to backup directory
- **Point-in-Time Recovery**: Transaction log backups

## Security

- **Encryption**: SQLite databases encrypted at rest
- **Access Control**: File-system permissions
- **Audit Logging**: Database access logged
- **Data Filtering**: Sensitive data excluded from subordinate databases

## Usage Examples

### Web Application Database
```bash
# Create new web app database
./create-webapp-db.sh my-app

# Sync data from TerminusDB
./sync-web-apps.sh my-app

# Query the database
sqlite3 web-apps/my-app.db "SELECT * FROM clients LIMIT 10;"
```

### Mobile Application Database
```bash
# Create mobile app database
./create-mobile-db.sh field-service

# Sync offline-capable data
./sync-mobile-apps.sh field-service

# Package for mobile deployment
./package-mobile-db.sh field-service
```
