/**
 * FAP-Goose: Minimal AI Agent Interface
 * Vanilla JavaScript implementation following FAP philosophy
 */

// Global namespace
window.fap = window.fap || {};
window.fap.goose = {
  websocket: null,
  isConnected: false,
  
  // Initialize the application
  init() {
    this.bindEvents();
    this.loadSystemInfo();
    console.log('🪿 Goose-FAP initialized');
  },

  // Bind event listeners
  bindEvents() {
    const connectBtn = document.getElementById('connect-btn');
    const disconnectBtn = document.getElementById('disconnect-btn');
    const sendBtn = document.getElementById('send-btn');
    const messageInput = document.getElementById('message-input');

    connectBtn?.addEventListener('click', () => this.connect());
    disconnectBtn?.addEventListener('click', () => this.disconnect());
    sendBtn?.addEventListener('click', () => this.sendMessage());
    
    messageInput?.addEventListener('keypress', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.sendMessage();
      }
    });
  },

  // Load system information
  async loadSystemInfo() {
    try {
      const response = await fetch('/health');
      const data = await response.json();
      
      if (data.success) {
        const versionEl = document.getElementById('version');
        const featuresEl = document.getElementById('features');
        
        if (versionEl) versionEl.textContent = data.data.version;
        if (featuresEl) featuresEl.textContent = data.data.features.join(', ');
      }
    } catch (error) {
      console.error('Failed to load system info:', error);
    }
  },

  // Connect to WebSocket
  connect() {
    if (this.websocket) {
      console.log('Already connected or connecting');
      return;
    }

    this.updateConnectionStatus('connecting');
    
    const wsUrl = 'ws://localhost:3002/ws';
    this.websocket = new WebSocket(wsUrl);

    this.websocket.onopen = () => {
      console.log('WebSocket connected');
      this.isConnected = true;
      this.updateConnectionStatus('connected');
      this.updateUI();
      this.loadTools();
    };

    this.websocket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('Failed to parse message:', error);
      }
    };

    this.websocket.onclose = () => {
      console.log('WebSocket disconnected');
      this.isConnected = false;
      this.websocket = null;
      this.updateConnectionStatus('disconnected');
      this.updateUI();
    };

    this.websocket.onerror = (error) => {
      console.error('WebSocket error:', error);
      this.updateConnectionStatus('disconnected');
    };
  },

  // Disconnect from WebSocket
  disconnect() {
    if (this.websocket) {
      this.websocket.close();
    }
  },

  // Update connection status display
  updateConnectionStatus(state) {
    const statusEl = document.querySelector('goose-status');
    if (statusEl) {
      statusEl.setAttribute('data-state', state);
      
      const statusText = {
        connected: 'Connected',
        disconnected: 'Disconnected',
        connecting: 'Connecting...'
      };
      
      statusEl.textContent = statusText[state] || state;
    }
  },

  // Update UI based on connection state
  updateUI() {
    const connectBtn = document.getElementById('connect-btn');
    const disconnectBtn = document.getElementById('disconnect-btn');
    const messageInput = document.getElementById('message-input');
    const sendBtn = document.getElementById('send-btn');

    if (connectBtn) connectBtn.disabled = this.isConnected;
    if (disconnectBtn) disconnectBtn.disabled = !this.isConnected;
    if (messageInput) messageInput.disabled = !this.isConnected;
    if (sendBtn) sendBtn.disabled = !this.isConnected;

    // Update button classes
    if (connectBtn) {
      connectBtn.className = this.isConnected ? '' : 'primary';
    }
  },

  // Load available tools
  async loadTools() {
    try {
      const response = await fetch('/api/mcp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          method: 'tools/list',
          params: {}
        })
      });

      const data = await response.json();
      
      if (data.success && data.data.tools) {
        this.displayTools(data.data.tools);
      }
    } catch (error) {
      console.error('Failed to load tools:', error);
    }
  },

  // Display tools in sidebar
  displayTools(tools) {
    const toolsList = document.getElementById('tools-list');
    if (!toolsList) return;

    toolsList.innerHTML = '';

    if (tools.length === 0) {
      const placeholder = document.createElement('goose-tool-placeholder');
      placeholder.textContent = 'No tools available';
      toolsList.appendChild(placeholder);
      return;
    }

    tools.forEach(tool => {
      const toolEl = document.createElement('goose-tool');
      toolEl.innerHTML = `
        <goose-tool-name>${tool.name}</goose-tool-name>
        <goose-tool-description>${tool.description || 'No description'}</goose-tool-description>
      `;
      
      toolEl.addEventListener('click', () => {
        this.insertToolCall(tool.name);
      });
      
      toolsList.appendChild(toolEl);
    });
  },

  // Insert tool call into message input
  insertToolCall(toolName) {
    const messageInput = document.getElementById('message-input');
    if (messageInput && !messageInput.disabled) {
      const currentValue = messageInput.value;
      const toolCall = `Use the ${toolName} tool to `;
      
      if (currentValue) {
        messageInput.value = currentValue + ' ' + toolCall;
      } else {
        messageInput.value = toolCall;
      }
      
      messageInput.focus();
      messageInput.setSelectionRange(messageInput.value.length, messageInput.value.length);
    }
  },

  // Send message
  sendMessage() {
    const messageInput = document.getElementById('message-input');
    if (!messageInput || !this.isConnected) return;

    const message = messageInput.value.trim();
    if (!message) return;

    // Add user message to chat
    this.addMessage('user', message);
    
    // Send to WebSocket
    if (this.websocket) {
      this.websocket.send(JSON.stringify({
        type: 'chat',
        message: message,
        timestamp: new Date().toISOString()
      }));
    }

    // Clear input
    messageInput.value = '';
  },

  // Handle incoming WebSocket messages
  handleMessage(message) {
    console.log('Received message:', message);

    switch (message.type) {
      case 'welcome':
        this.addMessage('system', message.message);
        break;
        
      case 'echo':
        this.addMessage('assistant', `Echo: ${message.original}`);
        break;
        
      case 'response':
        this.addMessage('assistant', message.content);
        break;
        
      default:
        console.log('Unknown message type:', message.type);
    }
  },

  // Add message to chat
  addMessage(role, content) {
    const messagesContainer = document.getElementById('messages');
    if (!messagesContainer) return;

    const messageEl = document.createElement('goose-message');
    messageEl.setAttribute('data-role', role);
    
    const bubbleEl = document.createElement('goose-message-bubble');
    bubbleEl.textContent = content;
    
    messageEl.appendChild(bubbleEl);
    messagesContainer.appendChild(messageEl);

    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
  },

  // Utility: Format timestamp
  formatTimestamp(timestamp) {
    return new Date(timestamp).toLocaleTimeString();
  },

  // Utility: Show loading state
  showLoading(element) {
    if (element) {
      element.classList.add('loading');
    }
  },

  // Utility: Hide loading state
  hideLoading(element) {
    if (element) {
      element.classList.remove('loading');
    }
  }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.fap.goose.init();
});

// Export for potential external use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = window.fap.goose;
}
