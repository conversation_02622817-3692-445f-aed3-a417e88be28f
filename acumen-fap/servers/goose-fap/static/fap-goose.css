/**
 * FAP-Goose: Minimal AI Agent Interface
 * Semantic custom elements for Goose integration
 */

/* Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: #fafafa;
  color: #111827;
  line-height: 1.5;
}

/* Goose App Layout */
goose-app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

goose-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 24px;
  background: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

goose-title {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
}

goose-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin-left: 12px;
}

goose-status {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

goose-status[data-state="connected"] {
  background: #dcfce7;
  color: #166534;
}

goose-status[data-state="disconnected"] {
  background: #fef2f2;
  color: #991b1b;
}

goose-status[data-state="connecting"] {
  background: #fef3c7;
  color: #92400e;
}

goose-status::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
}

/* Main Layout */
goose-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

goose-sidebar {
  width: 300px;
  background: #ffffff;
  border-right: 1px solid #e5e7eb;
  overflow-y: auto;
  padding: 16px;
}

goose-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Sidebar Sections */
goose-section {
  display: block;
  margin-bottom: 24px;
}

goose-section-title {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Connection Section */
goose-connection {
  display: block;
}

goose-connection-info {
  display: block;
}

goose-connection-url {
  display: block;
  font-family: monospace;
  font-size: 12px;
  color: #6b7280;
  background: #f9fafb;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 8px;
}

goose-connection-actions {
  display: flex;
  gap: 8px;
}

goose-connection-actions button {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background: #ffffff;
  color: #374151;
  cursor: pointer;
  font-size: 12px;
}

goose-connection-actions button:hover:not(:disabled) {
  background: #f9fafb;
}

goose-connection-actions button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

goose-connection-actions button.primary {
  background: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

goose-connection-actions button.primary:hover:not(:disabled) {
  background: #2563eb;
}

/* Tools Section */
goose-tools {
  display: block;
}

goose-tool {
  display: block;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  margin-bottom: 4px;
  cursor: pointer;
}

goose-tool:hover {
  background: #f9fafb;
}

goose-tool-name {
  display: block;
  font-weight: 500;
  color: #111827;
  font-size: 14px;
}

goose-tool-description {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-top: 2px;
}

goose-tool-placeholder {
  display: block;
  color: #9ca3af;
  font-style: italic;
  text-align: center;
  padding: 16px;
}

/* System Info */
goose-system-info {
  display: block;
}

goose-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  border-bottom: 1px solid #f3f4f6;
}

goose-info-item:last-child {
  border-bottom: none;
}

goose-info-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

goose-info-value {
  font-size: 12px;
  color: #111827;
  font-family: monospace;
}

/* Chat Interface */
goose-chat {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #ffffff;
}

goose-chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

goose-chat-title {
  font-weight: 600;
  color: #111827;
}

goose-chat-status {
  font-size: 12px;
  color: #6b7280;
}

goose-chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Messages */
goose-message {
  display: block;
  max-width: 80%;
}

goose-message[data-role="user"] {
  align-self: flex-end;
  text-align: right;
}

goose-message[data-role="assistant"] {
  align-self: flex-start;
}

goose-message[data-role="system"] {
  align-self: center;
  max-width: 90%;
}

goose-message-bubble {
  display: inline-block;
  padding: 12px 16px;
  border-radius: 16px;
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
}

goose-message[data-role="user"] goose-message-bubble {
  background: #3b82f6;
  color: white;
}

goose-message[data-role="assistant"] goose-message-bubble {
  background: #f3f4f6;
  color: #111827;
}

goose-message[data-role="system"] goose-message-bubble {
  background: #fef3c7;
  color: #92400e;
  font-style: italic;
  text-align: center;
}

/* Chat Input */
goose-chat-input {
  display: flex;
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  background: #fafafa;
  gap: 12px;
}

goose-chat-input input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 24px;
  background: white;
  font-size: 14px;
  outline: none;
}

goose-chat-input input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

goose-chat-input input:disabled {
  background: #f9fafb;
  color: #9ca3af;
  cursor: not-allowed;
}

goose-chat-input button {
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 24px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
}

goose-chat-input button:hover:not(:disabled) {
  background: #2563eb;
}

goose-chat-input button:disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

/* Footer */
goose-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  background: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

goose-footer-info {
  font-size: 12px;
  color: #6b7280;
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.error {
  color: #dc2626 !important;
  background: #fef2f2 !important;
}

.success {
  color: #059669 !important;
  background: #ecfdf5 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  goose-main {
    flex-direction: column;
  }
  
  goose-sidebar {
    width: 100%;
    max-height: 200px;
  }
  
  goose-header {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }
}
