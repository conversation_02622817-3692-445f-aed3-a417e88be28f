<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Goose-FAP: AI Agent Interface</title>
  <link rel="stylesheet" href="/static/fap-goose.css">
</head>
<body>
  <goose-app>
    <goose-header>
      <goose-title>🪿 Goose-FAP</goose-title>
      <goose-subtitle>Minimal AI Agent for FAP Monorepo</goose-subtitle>
      <goose-status data-state="disconnected">Disconnected</goose-status>
    </goose-header>

    <goose-main>
      <goose-sidebar>
        <goose-section>
          <goose-section-title>Connection</goose-section-title>
          <goose-connection>
            <goose-connection-info>
              <goose-connection-url>ws://localhost:3002/ws</goose-connection-url>
              <goose-connection-actions>
                <button id="connect-btn">Connect</button>
                <button id="disconnect-btn" disabled>Disconnect</button>
              </goose-connection-actions>
            </goose-connection-info>
          </goose-connection>
        </goose-section>

        <goose-section>
          <goose-section-title>MC<PERSON> Tools</goose-section-title>
          <goose-tools id="tools-list">
            <goose-tool-placeholder>Connect to see available tools</goose-tool-placeholder>
          </goose-tools>
        </goose-section>

        <goose-section>
          <goose-section-title>System Info</goose-section-title>
          <goose-system-info id="system-info">
            <goose-info-item>
              <goose-info-label>Version:</goose-info-label>
              <goose-info-value id="version">-</goose-info-value>
            </goose-info-item>
            <goose-info-item>
              <goose-info-label>Features:</goose-info-label>
              <goose-info-value id="features">-</goose-info-value>
            </goose-info-item>
          </goose-system-info>
        </goose-section>
      </goose-sidebar>

      <goose-content>
        <goose-chat>
          <goose-chat-header>
            <goose-chat-title>AI Assistant</goose-chat-title>
            <goose-chat-status id="chat-status">Ready</goose-chat-status>
          </goose-chat-header>

          <goose-chat-messages id="messages">
            <goose-message data-role="system">
              <goose-message-bubble>
                Welcome to Goose-FAP! This is a minimal AI agent interface built for the FAP monorepo.
                Connect to start chatting with the AI assistant.
              </goose-message-bubble>
            </goose-message>
          </goose-chat-messages>

          <goose-chat-input>
            <input type="text" id="message-input" placeholder="Type your message..." disabled>
            <button id="send-btn" disabled>Send</button>
          </goose-chat-input>
        </goose-chat>
      </goose-content>
    </goose-main>

    <goose-footer>
      <goose-footer-info>
        Powered by Goose AI Agent | FAP Monorepo Integration
      </goose-footer-info>
    </goose-footer>
  </goose-app>

  <script src="/static/fap-goose.js"></script>
</body>
</html>
