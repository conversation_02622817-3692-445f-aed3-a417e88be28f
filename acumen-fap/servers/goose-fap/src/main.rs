// Goose-FAP test app using shared FAP libraries
use fap_web::prelude::*;

#[derive(Debug, Serialize, Deserialize)]
struct HealthCheck {
    status: String,
    version: String,
    features: Vec<String>,
}

async fn health_check() -> <PERSON><PERSON><ApiResponse<HealthCheck>> {
    let mut features = vec!["web-interface".to_string(), "fap-libraries".to_string()];
    
    // Add conditional features if available
    #[cfg(feature = "ai-providers")]
    features.push("ai-providers".to_string());
    
    #[cfg(feature = "vector-db")]
    features.push("vector-db".to_string());
    
    #[cfg(feature = "full-goose")]
    features.push("full-goose".to_string());

    Json(ApiResponse::success(HealthCheck {
        status: "healthy".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        features,
    }))
}

#[derive(Debug, Deserialize)]
struct MCPRequest {
    method: String,
    params: serde_json::Value,
}

async fn mcp_handler(Json(request): Json<MCPRequest>) -> <PERSON><PERSON><ApiResponse<serde_json::Value>> {
    println!("MCP request: {} with params: {}", request.method, request.params);
    
    // Placeholder MCP handling - replace with actual MCP client calls
    match request.method.as_str() {
        "tools/list" => {
            let tools = serde_json::json!({
                "tools": [
                    {
                        "name": "echo",
                        "description": "Echo back the input",
                        "inputSchema": {
                            "type": "object",
                            "properties": {
                                "message": {"type": "string"}
                            }
                        }
                    }
                ]
            });
            Json(ApiResponse::success(tools))
        }
        "tools/call" => {
            let result = serde_json::json!({
                "content": [
                    {
                        "type": "text",
                        "text": format!("Echo: {}", request.params)
                    }
                ]
            });
            Json(ApiResponse::success(result))
        }
        _ => {
            let error_msg = format!("Unknown method: {}", request.method);
            Json(ApiResponse {
                success: false,
                data: None,
                error: Some(error_msg),
                timestamp: Utc::now().to_rfc3339(),
            })
        }
    }
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    println!("Starting Goose-FAP server...");

    // Build our application with routes
    let app = Router::new()
        .route("/health", get(health_check))
        .route("/mcp", post(mcp_handler));

    let addr = std::net::SocketAddr::from(([127, 0, 0, 1], 3002));
    println!("Goose-FAP server listening on {}", addr);

    let listener = tokio::net::TcpListener::bind(addr).await?;
    axum::serve(listener, app).await?;

    Ok(())
}
