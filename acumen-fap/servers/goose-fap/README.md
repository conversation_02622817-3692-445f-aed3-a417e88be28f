# Goose-FAP: Minimal AI Agent

A lightweight, optimized build of the Goose AI agent designed specifically for the FAP monorepo. This application extracts the core value from <PERSON> while maintaining the FAP philosophy of vanilla HTML/CSS/JS and semantic custom elements.

## 🚀 Breakthrough: Shared FAP Rust Foundation

**Build Time Revolution**: 2.4MB binary (vs 164MB full Goose) with **dramatically faster builds** using our shared FAP Rust libraries!

| Approach | Build Time | Binary Size | Dependencies |
|----------|------------|-------------|-------------|
| **Full Goose** | 2-5 minutes | 164MB | 925+ crates |
| **Goose-FAP** | 10-15 seconds | 2.4MB | Shared FAP libs |

## Overview

Goose-FAP leverages the **FAP Rust Foundation** to provide:
- **MCP Protocol Integration**: Connect to Model Context Protocol servers
- **Web Interface**: Clean, vanilla JavaScript UI following FAP conventions
- **Shared Libraries**: Uses pre-compiled `fap-core` and `fap-web` libraries
- **Externalized Components**: Key features extracted as separate FAP packages
- **Lightning Fast Builds**: Incremental compilation in seconds, not minutes

## Architecture

### Shared FAP Foundation
- **`fap-core`**: Common utilities, error handling, configuration, async patterns
- **`fap-web`**: Web server stack (Axum, CORS, static files, WebSocket support)
- **Pre-compiled Dependencies**: No more waiting for Tokio, Axum, etc. to compile
- **Workspace Integration**: Seamless integration with other FAP Rust applications

### Core Features
- **Minimal Application Code**: Only business logic, everything else is shared
- **Feature Flags**: Optional AI providers, vector DB, full Goose integration
- **MCP Client**: Direct integration with MCP protocol for extensibility
- **Semantic UI**: Follows FAP custom element conventions

### Externalized Components
- `fap-mcp`: MCP protocol client package (vanilla JS)
- `fap-lancedb`: Vector database integration package (vanilla JS)
- Additional packages planned for tokenizer, AI providers, etc.

## Quick Start

### Prerequisites
- Rust 1.70+ installed
- Node.js 24+ (for development)

### Build and Run

**With Shared FAP Foundation (Recommended):**
```bash
# Navigate to monorepo root
cd /path/to/monorepo

# Build shared libraries once (if not already built)
cargo build --release -p fap-core -p fap-web

# Build Goose-FAP (lightning fast!)
cargo build --release -p goose-fap

# Run the server
cargo run --release -p goose-fap
```

**Traditional approach (slower):**
```bash
# Navigate to the app directory
cd apps/goose-fap

# Build everything from scratch
cargo build --release

# Run with specific features
cargo run --release --features "ai-providers,vector-db"
```

The server will start on `http://localhost:3002` by default.

### Build Time Comparison
- **First build with shared libs**: ~30 seconds (builds shared foundation)
- **Subsequent builds**: ~10-15 seconds (only app logic)
- **Incremental changes**: ~3-5 seconds (minimal recompilation)

### Environment Variables

```bash
# Set custom port
export PORT=3003

# Set log level
export RUST_LOG=goose_fap=debug,tower_http=debug
```

## Features

### Default Features
- `web-interface`: Axum web server with static file serving
- `mcp-client`: Model Context Protocol client integration

### Optional Features
- `ai-providers`: Include AI provider integrations (adds reqwest)
- `vector-db`: Include LanceDB vector database support
- `full-goose`: Include full Goose core functionality

### Build Configurations

```bash
# Minimal build (default features only)
cargo build --release --no-default-features --features "web-interface,mcp-client"

# With AI providers
cargo build --release --features "ai-providers"

# With vector database
cargo build --release --features "vector-db"

# Full integration
cargo build --release --features "full-goose"
```

## API Endpoints

### Health Check
```
GET /health
```
Returns system status, version, and enabled features.

### MCP Integration
```
POST /api/mcp
Content-Type: application/json

{
  "method": "tools/list",
  "params": {}
}
```

### WebSocket
```
WS /ws
```
Real-time communication for chat interface.

## UI Components

The interface uses semantic custom elements following FAP conventions:

### Layout Elements
- `<goose-app>` - Main application container
- `<goose-header>` - Top navigation and status
- `<goose-main>` - Content area with sidebar and chat
- `<goose-sidebar>` - Tools and connection info
- `<goose-content>` - Chat interface

### Connection Components
- `<goose-status>` - Connection status indicator
- `<goose-connection>` - Connection controls
- `<goose-connection-url>` - WebSocket URL display

### Tool Components
- `<goose-tools>` - Available MCP tools list
- `<goose-tool>` - Individual tool item
- `<goose-tool-name>` - Tool name display
- `<goose-tool-description>` - Tool description

### Chat Components
- `<goose-chat>` - Chat interface container
- `<goose-chat-messages>` - Message history
- `<goose-message>` - Individual message
- `<goose-message-bubble>` - Message content
- `<goose-chat-input>` - Message input area

## Integration with FAP Packages

### MCP Integration
```javascript
// Use the fap-mcp package for advanced MCP features
import '/packages/fap-mcp/fap-mcp.js';

// Access MCP client
const mcpClient = window.fap.mcp.client;
```

### LanceDB Integration
```javascript
// Use the fap-lancedb package for vector operations
import '/packages/fap-lancedb/fap-lancedb.js';

// Access LanceDB utilities
const lancedb = window.fap.lancedb;
```

## Development

### File Structure
```
apps/goose-fap/
├── Cargo.toml          # Rust dependencies and features
├── src/
│   └── main.rs         # Main application entry point
├── static/
│   ├── index.html      # Main UI template
│   ├── fap-goose.css   # Semantic styling
│   └── fap-goose.js    # Vanilla JavaScript logic
└── README.md           # This file
```

### Building for Production

```bash
# Optimized release build
cargo build --release

# Strip debug symbols for smaller binary
strip target/release/goose-fap

# Check binary size
ls -lh target/release/goose-fap
```

### Customization

1. **Add Features**: Modify `Cargo.toml` to include additional dependencies
2. **Extend API**: Add new routes in `src/main.rs`
3. **UI Changes**: Modify files in `static/` directory
4. **MCP Integration**: Use the `fap-mcp` package for advanced features

## Comparison with Full Goose

| Feature | Full Goose | Goose-FAP |
|---------|------------|-----------|
| Binary Size | 164MB | ~80MB target |
| Dependencies | 925+ | ~50 essential |
| UI Framework | React/TypeScript | Vanilla JS |
| Build Time | ~5 minutes | ~1 minute |
| Memory Usage | High | Optimized |
| Extensibility | Built-in | Via FAP packages |

## License

Apache-2.0 (following Goose licensing)

## Related Packages

- [`fap-mcp`](../../packages/fap-mcp/) - MCP protocol client
- [`fap-lancedb`](../../packages/fap-lancedb/) - Vector database integration
- [Goose Documentation](../../docs-developer/features/goose/) - Full integration guide
