[package]
name = "goose-fap"
version.workspace = true
edition.workspace = true
authors.workspace = true
license.workspace = true
description = "Minimal Goose build for FAP monorepo - optimized and externalized"

[dependencies]
# Use our working shared FAP libraries
fap-core = { path = "../../rust-libs/fap-core", features = ["async", "config"] }
fap-web = { path = "../../rust-libs/fap-web" }

# Basic web server dependencies
tokio.workspace = true
axum.workspace = true
tower-http.workspace = true
serde.workspace = true
serde_json.workspace = true
tracing.workspace = true
tracing-subscriber.workspace = true
anyhow.workspace = true

[[bin]]
name = "goose-fap"
path = "src/main.rs"

[profile.release]
# Optimize for size and speed
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true
