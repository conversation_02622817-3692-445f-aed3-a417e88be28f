# TerminusDB Environment Configuration
# Copy this file to environment.env and fill in your values

# Database administrator password (required)
TERMINUSDB_ADMIN_PASS=your-secure-password-here

# OpenAI API key (optional - for AI features)
OPENAI_KEY=your-openai-key-here

# Server configuration
TERMINUSDB_SERVER_NAME=Acumen Desktop FAP
TERMINUSDB_SERVER_DB_PATH=../data
TERMINUSDB_LOG_LEVEL=info

# Network configuration
TERMINUSDB_SERVER_PORT=6363
TERMINUSDB_SERVER_WORKERS=8

# CORS configuration (for web interface)
TERMINUSDB_CORS_ALLOW_ORIGIN=*
