name: Build and release
on:
  push:
    branches:
      - main
    tags:
      - v*
  pull_request:

jobs:
  setup-build-publish-deploy:
    name: Setup, Build, Publish, and Deploy
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: 18
        cache: 'npm'
    - run: npm i --legacy-peer-deps
    - name : Build Documents-UI
      run : |
        cd packages/tdb-documents-ui
        npm run build
    - name : Build React Table
      run : |
        cd packages/tdb-react-table
        npm run build
    - name: Build
      if: ${{ github.ref == 'refs/heads/main' || github.event_name == 'pull_request' }}
      env:
        NODE_OPTIONS: --max_old_space_size=4096
      run: |
        cd packages/tdb-dashboard
        echo '
            CONNECTION_TYPE="REMOTE"
            TERMINUSDB_SERVER="https://cloud-dev.terminusdb.com/"
            FEEDBACK_URL="https://cloud-dev.terminusdb.com/"
            REACT_APP_BASE_ROUTER=""
            AUTH0_CLIENT_ID="s6iYFJzoSedUHGUUaRYCGGWXWEPHkd02"
            AUTH0_DOMAIN="signup-dev.terminusdb.com"
            AUDIENCE="https://terminuscloud/users"
        ' > .env
        npm run build-terminusx
    - name: Build prod
      if: startsWith(github.ref, 'refs/tags/v')
      env:
        NODE_OPTIONS: --max_old_space_size=4096
      run: |
        cd packages/tdb-dashboard
        echo '
            CONNECTION_TYPE="REMOTE"
            TERMINUSDB_SERVER="https://cloud.terminusdb.com/"
            FEEDBACK_URL="https://cloud.terminusdb.com/"
            REACT_APP_BASE_ROUTER=""
            AUTH0_CLIENT_ID="365PsB0AmUmB2odSFwezWkX31BZv6PoH"
            AUTH0_DOMAIN="signup.terminusdb.com"
            AUDIENCE="https://terminuscloud/users"
        ' > .env
        npm run build-terminusx
    - name: Upload tdb-dashboard to dev s3
      if: ${{ github.event_name != 'pull_request' && github.ref == 'refs/heads/main' }}
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      run: aws s3 sync packages/tdb-dashboard/dist/ s3://${{ secrets.S3_DEV_BUCKET }} --region=${{ secrets.AWS_REGION }}
    - name: Upload tdb-dashboard to prod s3
      if: ${{ github.event_name != 'pull_request' && startsWith(github.ref, 'refs/tags/v') }}
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      run: aws s3 sync packages/tdb-dashboard/dist/ s3://${{ secrets.S3_PROD_BUCKET }} --region=${{ secrets.AWS_REGION }}
    - name: Clean cloudflare cache
      if: ${{ github.event_name != 'pull_request' }}
      run: python3 .ci/clean_cloudflare_cache.py ${{ github.ref }}
      env:
        CLOUDFLARE_EMAIL: ${{ secrets.CLOUDFLARE_EMAIL }}
        CLOUDFLARE_ZONE: ${{ secrets.CLOUDFLARE_ZONE }}
        CLOUDFLARE_API_KEY: ${{ secrets.CLOUDFLARE_API_KEY }}

  build_for_local:
    name: Build local dashboard
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: 18
        cache: 'npm'
    - run: npm i --legacy-peer-deps
    - name : Build Documents-UI
      run : |
        cd packages/tdb-documents-ui
        npm run build
    - name : Build React Table
      run : |
        cd packages/tdb-react-table
        npm run build
    - name: Build local dashboard
      if: ${{ github.ref == 'refs/heads/main' || github.event_name == 'pull_request' || startsWith(github.ref, 'refs/tags/v') }}
      env:
        NODE_OPTIONS: --max_old_space_size=4096
      run: |
        cd packages/tdb-dashboard
        echo '
            CONNECTION_TYPE="LOCAL"
            TERMINUSDB_KEY=root
            FEEDBACK_URL="https://cloud.terminusdb.com/"
            BASE_URL="dashboard"
        ' > .env
        npm run build
        cd dist
        tar caf release.tar.gz *


    - uses: actions/upload-artifact@v3
      with:
        name: dashboard
        path: packages/tdb-dashboard/dist/release.tar.gz

    - name: Create Release
      if: startsWith(github.ref, 'refs/tags/v')
      uses: ncipollo/release-action@v1.10.0
      with:
        artifacts: packages/tdb-dashboard/dist/release.tar.gz
  npm:
    name: Publish packages to npm
    runs-on: ubuntu-latest
    needs: [setup-build-publish-deploy]
    steps:
    - name: Checkout
      uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: 18
        registry-url: 'https://registry.npmjs.org'
    - name: Build packages
      env:
        NODE_OPTIONS: --max_old_space_size=4096
      run:  npm i --legacy-peer-deps && npm run build --ws
    - name: Publish workspaces
      if: ${{ github.event_name != 'pull_request' && startsWith(github.ref, 'refs/tags/v') }}
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
      run: |
        npm publish --workspace packages/tdb-access-control-component \
          --workspace packages/tdb-documents-ui \
          --workspace packages/tdb-react-components \
          --workspace packages/tdb-react-table
