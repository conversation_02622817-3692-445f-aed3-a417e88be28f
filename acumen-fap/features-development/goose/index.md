# Goose Project Markdown File Index

This index provides an overview of all Markdown files in the Goose project, organized by their relative paths, along with a brief description of their content.

## Root Directory

- **[ACCEPTABLE_USAGE.md](../../../_clones/goose/ACCEPTABLE_USAGE.md):** Defines the acceptable usage policy for <PERSON>, outlining prohibited activities and responsible use.
- **[ARCHITECTURE.md](../../../_clones/goose/ARCHITECTURE.md):** Provides a high-level overview of the Goose architecture, focusing on the extension system and core design decisions.
- **[BUILDING_LINUX.md](../../../_clones/goose/BUILDING_LINUX.md):** Contains instructions for building the Goose Desktop application from source on various Linux distributions.
- **[CONTRIBUTING.md](../../../_clones/goose/CONTRIBUTING.md):** A guide for contributing to the Goose project, covering prerequisites, getting started with <PERSON><PERSON> and Node, and the pull request process.
- **[GEMINI.md](../../../_clones/goose/GEMINI.md):** A file used by the Gemini CLI for internal operations and to confirm access.
- **[README.md](../../../_clones/goose/README.md):** The main README for the Goose project, providing a high-level overview, quick links, and community information.
- **[RELEASE.md](../../../_clones/goose/RELEASE.md):** Outlines the process for making a new release of Goose, including regular and patch releases.
- **[run_cross_local.md](../../../_clones/goose/run_cross_local.md):** Provides instructions for running `cross` to test release builds locally for different targets.
- **[SECURITY.md](../../../_clones/goose/SECURITY.md):** Important security information and warnings regarding the use of Goose as a developer agent.

## .github/ISSUE_TEMPLATE

- **[bug_report.md](../../../_clones/goose/.github/ISSUE_TEMPLATE/bug_report.md):** Template for reporting bugs in the Goose project, requesting steps to reproduce and expected behavior.
- **[feature_request.md](../../../_clones/goose/.github/ISSUE_TEMPLATE/feature_request.md):** Template for suggesting new features or enhancements for the Goose project.

## bin

- **[README.hermit.md](../../../_clones/goose/bin/README.hermit.md):** Explains the Hermit environment used for managing dependencies in the Goose project.

## crates/goose

- **[src/prompts/mock.md](../../../_clones/goose/crates/goose/src/prompts/mock.md):** A placeholder prompt used only for testing purposes.
- **[src/prompts/plan.md](../../../_clones/goose/crates/goose/src/prompts/plan.md):** Defines the role of the "planner" AI, outlining guidelines for creating step-by-step plans or clarifying questions.
- **[src/prompts/recipe.md](../../../_clones/goose/crates/goose/src/prompts/recipe.md):** Provides instructions and examples for generating a recipe from a conversation, including instructions and activities.
- **[src/prompts/subagent_system.md](../../../_clones/goose/crates/goose/src/prompts/subagent_system.md):** Defines the role and guidelines for specialized subagents within the Goose AI framework.
- **[src/prompts/summarize_oneshot.md](../../../_clones/goose/crates/goose/src/prompts/summarize_oneshot.md):** Instructions for generating a detailed summary of a conversation, including user intent, technical concepts, and pending tasks.
- **[src/prompts/system.md](../../../_clones/goose/crates/goose/src/prompts/system.md):** The core system prompt for the Goose AI agent, outlining its general purpose, extensions, and response guidelines.
- **[src/prompts/system_gpt_4.1.md](../../../_clones/goose/crates/goose/src/prompts/system_gpt_4.1.md):** A specialized system prompt for Goose when used with GPT-4.1, providing critical instructions for task resolution and tool usage.

## crates/goose-bench

- **[README.md](../../../_clones/goose/crates/goose-bench/README.md):** Provides an overview of the Goose benchmarking framework, including how to run benchmarks and configure them.

## crates/goose-cli

- **[WEB_INTERFACE.md](../../../_clones/goose/crates/goose-cli/WEB_INTERFACE.md):** Describes the lightweight web-based interface for the Goose CLI, its usage, features, and limitations.

## crates/goose-mcp

- **[README.md](../../../_clones/goose/crates/goose-mcp/README.md):** Instructions for testing the `goose-mcp` crate with the MCP Inspector.
- **[src/developer/editor_models/EDITOR_API_EXAMPLE.md](../../../_clones/goose/crates/goose-mcp/src/developer/editor_models/EDITOR_API_EXAMPLE.md):** Explains how to configure and use AI models for enhanced code editing through the `str_replace` command.
- **[src/tutorial/tutorials/build-mcp-extension.md](../../../_clones/goose/crates/goose-mcp/src/tutorial/tutorials/build-mcp-extension.md):** A tutorial guiding users through building an MCP extension using Python, TypeScript, or Kotlin SDKs.
- **[src/tutorial/tutorials/first-game.md](../../../_clones/goose/crates/goose-mcp/src/tutorial/tutorials/first-game.md):** A tutorial providing a framework for guiding a user through building their first simple game, such as a Flappy Bird clone.

## crates/goose-server

- **[ALLOWLIST.md](../../../_clones/goose/crates/goose-server/ALLOWLIST.md):** Defines the format and usage of the Goose Extension Allowlist, which controls which extensions can be installed.

## crates/mcp-client

- **[README.md](../../../_clones/goose/crates/mcp-client/README.md):** Instructions for testing the `mcp-client` crate with stdio and SSE transports.

## crates/mcp-server

- **[README.md](../../../_clones/goose/crates/mcp-server/README.md):** Instructions for testing the `mcp-server` crate with the MCP Inspector.

## documentation

- **[README.md](../../../_clones/goose/documentation/README.md):** The README for the Docusaurus-based documentation website, covering installation, local development, and deployment.

## documentation/blog

- **[README.md](../../../_clones/goose/documentation/blog/README.md):** A guide for writing and structuring blog posts for the Goose documentation site.
- **[2024-11-22-screenshot-driven-development/index.md](../../../_clones/goose/documentation/blog/2024-11-22-screenshot-driven-development/index.md):** Explores how Goose can help build web applications directly from screenshots, transforming design into functional code.
- **[2024-12-06-previewing-goose-v10-beta/index.md](../../../_clones/goose/documentation/blog/2024-12-06-previewing-goose-v10-beta/index.md):** Provides a sneak peek at the upcoming Goose v1.0 Beta, highlighting enhanced local-first capabilities and improved extensibility.
- **[2024-12-10-connecting-ai-agents-to-your-systems-with-mcp/index.md](../../../_clones/goose/documentation/blog/2024-12-10-connecting-ai-agents-to-your-systems-with-mcp/index.md):** Explains how the Model Context Protocol (MCP) enables AI agents to securely interact with existing tools and data sources.
- **[2024-12-11-resolving-ci-issues-with-goose-a-practical-walkthrough/index.md](../../../_clones/goose/documentation/blog/2024-12-11-resolving-ci-issues-with-goose-a-practical-walkthrough/index.md):** A practical walkthrough on how Goose can help diagnose and resolve CI/CD pipeline failures.
- **[2025-01-28-introducing-codename-goose/index.md](../../../_clones/goose/documentation/blog/2025-01-28-introducing-codename-goose/index.md):** Introduces Codename Goose as a local, extensible, open-source AI agent for automating engineering tasks.
- **[2025-02-17-agentic-ai-mcp/index.md](../../../_clones/goose/documentation/blog/2025-02-17-agentic-ai-mcp/index.md):** Discusses how the Model Context Protocol (MCP) enables AI agents to interact with external systems and data sources.
- **[2025-02-21-gooseteam-mcp/index.md](../../../_clones/goose/documentation/blog/2025-02-21-gooseteam-mcp/index.md):** Explains how to orchestrate multiple AI agents using MCP to tackle complex tasks collaboratively.
- **[2025-03-06-goose-tips/index.md](../../../_clones/goose/documentation/blog/2025-03-06-goose-tips/index.md):** Provides essential tips for optimizing Goose experience, including managing sessions, extensions, and permissions.
- **[2025-03-10-goose-calls-vyop/index.md](../../../_clones/goose/documentation/blog/2025-03-10-goose-calls-vyop/index.md):** Explains how Goose integrates with Vyop to automate customer support, from triaging tickets to generating responses.
- **[2025-03-12-goose-figma-mcp/index.md](../../../_clones/goose/documentation/blog/2025-03-12-goose-figma-mcp/index.md):** Shows how Goose can turn Figma designs into code with the Figma extension.
- **[2025-03-14-goose-ollama/index.md](../../../_clones/goose/documentation/blog/2025-03-14-goose-ollama/index.md):** Discusses integrating Goose with Ollama for a fully local AI experience.
- **[2025-03-18-goose-langfuse/index.md](../../../_clones/goose/documentation/blog/2025-03-18-goose-langfuse/index.md):** Explains how Goose catches AI errors with Langfuse's observability tools.
- **[2025-03-19-better-ai-prompting/index.md](../../../_clones/goose/documentation/blog/2025-03-19-better-ai-prompting/index.md):** A guide on how to get the best responses from your AI agent through various prompting styles.
- **[2025-03-20-asana-calendar-mcp/index.md](../../../_clones/goose/documentation/blog/2025-03-20-asana-calendar-mcp/index.md):** Shows how Goose can plan a week using Asana and Google Calendar MCPs.
- **[2025-03-21-goose-vscode/index.md](../../../_clones/goose/documentation/blog/2025-03-21-goose-vscode/index.md):** Explores connecting Goose directly to VS Code with the VS Code MCP.
- **[2025-03-26-mcp-security/index.md](../../../_clones/goose/documentation/blog/2025-03-26-mcp-security/index.md):** Provides guidance on how to determine if an MCP server is safe to use.
- **[2025-03-28-vibe-coding-with-goose/index.md](../../../_clones/goose/documentation/blog/2025-03-28-vibe-coding-with-goose/index.md):** Explores the Speech MCP server that enables voice-controlled coding and natural conversation with AI agents.
- **[2025-03-31-goose-benchmark/index.md](../../../_clones/goose/documentation/blog/2025-03-31-goose-benchmark/index.md):** Presents the first Goose agent benchmark tests, including toolshim performance analysis.
- **[2025-03-31-securing-mcp/index.md](../../../_clones/goose/documentation/blog/2025-03-31-securing-mcp/index.md):** Discusses building secure and capable AI integrations with MCP at Block.
- **[2025-04-01-mcp-nondevs/index.md](../../../_clones/goose/documentation/blog/2025-04-01-mcp-nondevs/index.md):** Explains what MCP is for non-developers and how it can be used to save time on tasks.
- **[2025-04-01-top-5-mcp-servers/index.md](../../../_clones/goose/documentation/blog/2025-04-01-top-5-mcp-servers/index.md):** Lists the top 5 MCP servers used by a developer with Goose for automating workflows.
- **[2025-04-08-vibe-code-responsibly/index.md](../../../_clones/goose/documentation/blog/2025-04-08-vibe-code-responsibly/index.md):** Provides guidance on how to vibe code responsibly with Goose, protecting code and team.
- **[2025-04-10-visual-guide-mcp/index.md](../../../_clones/goose/documentation/blog/2025-04-10-visual-guide-mcp/index.md):** A visual breakdown of the MCP ecosystem, explaining how AI agents, tools, and models work together.
- **[2025-04-11-finetuning-toolshim/index.md](../../../_clones/goose/documentation/blog/2025-04-11-finetuning-toolshim/index.md):** Addresses performance limitations in models without native tool calling support through finetuning toolshim models.
- **[2025-04-14-community-atruelight4/index.md](../../../_clones/goose/documentation/blog/2025-04-14-community-atruelight4/index.md):** Highlights a community contribution that improved Goose's handling of Windows-specific file paths.
- **[2025-04-21-practical-use-cases-of-ai/index.md](../../../_clones/goose/documentation/blog/2025-04-21-practical-use-cases-of-ai/index.md):** Presents practical ways to use AI agents for everyday tasks, from conference planning to prepping podcasts.
- **[2025-04-22-community-bestcodes/index.md](../../../_clones/goose/documentation/blog/2025-04-22-community-bestcodes/index.md):** Showcases how a small contribution can lead to significant improvements in an open-source project.
- **[2025-04-22-mcp-is-rewriting-the-rules-of-api-integration/index.md](../../../_clones/goose/documentation/blog/2025-04-22-mcp-is-rewriting-the-rules-of-api-integration/index.md):** Discusses how MCP is modernizing API infrastructure with AI agents.
- **[2025-04-23-things-need-to-know/index.md](../../../_clones/goose/documentation/blog/2025-04-23-things-need-to-know/index.md):** Outlines essential information for getting started with Goose, including LLM choice and MCP servers.
- **[2025-05-06-recipe-for-success/index.md](../../../_clones/goose/documentation/blog/2025-05-06-recipe-for-success/index.md):** Explores how recipes can be used to scale agentic workflows and share knowledge.
- **[2025-05-09-developers-ai-playbook-for-team-efficiency/index.md](../../../_clones/goose/documentation/blog/2025-05-09-developers-ai-playbook-for-team-efficiency/index.md):** Discusses how AI-driven "plays" can form a starter "playbook" for dev teams.
- **[2025-05-12-local-goose-qwen3/index.md](../../../_clones/goose/documentation/blog/2025-05-12-local-goose-qwen3/index.md):** Explains how to run AI commands locally with Goose and Qwen3 for fast, offline tool execution.
- **[2025-05-20-goose-gets-a-drivers-license/index.md](../../../_clones/goose/documentation/blog/2025-05-20-goose-gets-a-drivers-license/index.md):** Describes how Goose can control a MakeBlock mbot2 rover through MQTT and MCP.
- **[2025-05-22-llm-agent-readiness/index.md](../../../_clones/goose/documentation/blog/2025-05-22-llm-agent-readiness/index.md):** Provides prompts to test an LLM's capabilities for use with AI agents.
- **[2025-05-22-manage-local-host-conflicts-with-goose/index.md](../../../_clones/goose/documentation/blog/2025-05-22-manage-local-host-conflicts-with-goose/index.md):** Shows how Goose can manage localhost port conflicts.
- **[2025-06-02-goose-panther-mcp/index.md](../../../_clones/goose/documentation/blog/2025-06-02-goose-panther-mcp/index.md):** Overview of how Block leverages Goose and Panther MCP to democratize security detection engineering.
- **[2025-06-05-whats-in-my-goosehints-file/index.md](../../../_clones/goose/documentation/blog/2025-06-05-whats-in-my-goosehints-file/index.md):** Discusses the use of `.goosehints` vs. Memory Extension for persistent context.
- **[2025-06-16-multi-model-in-goose/index.md](../../../_clones/goose/documentation/blog/2025-06-16-multi-model-in-goose/index.md):** Explains how Goose uses multiple LLMs within a single task for optimized performance.
- **[2025-06-19-isolated-development-environments/index.md](../../../_clones/goose/documentation/blog/2025-06-19-isolated-development-environments/index.md):** Describes how Goose can create isolated dev environments with `container-use`.
- **[2025-06-27-everyday-usecases-ai/index.md](../../../_clones/goose/documentation/blog/2025-06-27-everyday-usecases-ai/index.md):** Presents everyday use cases for AI agents that save time on mundane tasks.
- **[2025-07-28-ai-to-ai/index.md](../../../_clones/goose/documentation/blog/2025-07-28-ai-to-ai/index.md):** Explores how Goose Desktop and CLI can collaborate through AI-to-AI conversations.
- **[2025-07-28-streamlining-detection-development-with-goose-recipes/index.md](../../../_clones/goose/documentation/blog/2025-07-28-streamlining-detection-development-with-goose-recipes/index.md):** Guides on using recipes in Goose to streamline security detection development.
- **[2025-07-29-openrouter-unlocks-workshops/index.md](../../../_clones/goose/documentation/blog/2025-07-29-openrouter-unlocks-workshops/index.md):** Explains how OpenRouter provides frictionless LLM access for Goose workshops.
- **[2025-08-04-mcp-jupyter-server/index.md](../../../_clones/goose/documentation/blog/2025-08-04-mcp-jupyter-server/index.md):** Discusses how MCP Jupyter Server enables AI agents to work with Jupyter notebooks.

## documentation/docs

- **[quickstart.md](../../../_clones/goose/documentation/docs/quickstart.md):** A quick tutorial to get started with Goose, covering installation, LLM configuration, and building a small app.
- **[troubleshooting.md](../../../_clones/goose/documentation/docs/troubleshooting.md):** Provides solutions for common issues encountered when using Goose.

## documentation/docs/experimental

- **[goose-mobile.md](../../../_clones/goose/documentation/docs/experimental/goose-mobile.md):** Describes Goose Mobile, an experimental Android project for automating multistep tasks on a phone.
- **[index.md](../../../_clones/goose/documentation/docs/experimental/index.md):** An overview of experimental features and projects in Goose, including subagents and the Ollama Tool Shim.
- **[ollama.md](../../../_clones/goose/documentation/docs/experimental/ollama.md):** Explains the Ollama tool shim, an experimental feature enabling tool calling for models without native support.
- **[subagents.md](../../../_clones/goose/documentation/docs/experimental/subagents.md):** Describes subagents as independent instances for executing tasks with process isolation and context preservation.
- **[vs-code-extension.md](../../../_clones/goose/documentation/docs/experimental/vs-code-extension.md):** Covers the installation and usage of the Goose VS Code Extension for integrating Goose within VS Code.

## documentation/docs/getting-started

- **[installation.md](../../../_clones/goose/documentation/docs/getting-started/installation.md):** Instructions for installing Goose on macOS, Linux, and Windows, including CLI and Desktop versions.
- **[providers.md](../../../_clones/goose/documentation/docs/getting-started/providers.md):** Lists supported LLM providers and how to configure them with Goose, including custom OpenAI endpoints and local LLMs.
- **[using-extensions.md](../../../_clones/goose/documentation/docs/getting-started/using-extensions.md):** Explains how to use, add, enable, disable, and develop extensions for Goose.

## documentation/docs/goose-architecture

- **[error-handling.md](../../../_clones/goose/documentation/docs/goose-architecture/error-handling.md):** Explains how Goose handles errors, including traditional and agent-specific errors.
- **[extensions-design.md](../../../_clones/goose/documentation/docs/goose-architecture/extensions-design.md):** Describes the design and implementation of the Extensions framework in Goose.
- **[goose-architecture.md](../../../_clones/goose/documentation/docs/goose-architecture/goose-architecture.md):** Provides an overview of Goose's architecture, including its components and interactive loop.

## documentation/docs/guides

- **[allowlist.md](../../../_clones/goose/documentation/docs/guides/allowlist.md):** Explains how to create an allowlist of safe extensions for Goose Desktop and CLI.
- **[cli-providers.md](../../../_clones/goose/documentation/docs/guides/cli-providers.md):** Describes how Goose supports pass-through providers that integrate with existing CLI tools like Claude Code and Gemini CLI.
- **[config-file.md](../../../_clones/goose/documentation/docs/guides/config-file.md):** Explains the YAML configuration file used by Goose to manage settings and extensions.
- **[creating-plans.md](../../../_clones/goose/documentation/docs/guides/creating-plans.md):** Details how to create structured plans for Goose to follow, including the use of the `/plan` command.
- **[environment-variables.md](../../../_clones/goose/documentation/docs/guides/environment-variables.md):** Lists environment variables for configuring Goose, including model settings, session management, and experimental features.
- **[file-management.md](../../../_clones/goose/documentation/docs/guides/file-management.md):** Covers efficient file access and management in Goose, including quick file search and best practices for safe file operations.
- **[goose-cli-commands.md](../../../_clones/goose/documentation/docs/guides/goose-cli-commands.md):** Provides a list of available CLI commands for managing sessions, configurations, and extensions.
- **[goose-permissions.md](../../../_clones/goose/documentation/docs/guides/goose-permissions.md):** Explains Goose's permission modes for controlling its autonomy when modifying files and using extensions.
- **[handling-llm-rate-limits-with-goose.md](../../../_clones/goose/documentation/docs/guides/handling-llm-rate-limits-with-goose.md):** Discusses how to manage LLM rate limits when using Goose, including using OpenRouter.
- **[logs.md](../../../_clones/goose/documentation/docs/guides/logs.md):** Describes Goose's logging system, including command history, session records, and system logs.
- **[managing-goose-sessions.md](../../../_clones/goose/documentation/docs/guides/managing-goose-sessions.md):** Covers how to start, exit, resume, search, and export Goose sessions.
- **[managing-projects.md](../../../_clones/goose/documentation/docs/guides/managing-projects.md):** Explains how Goose Projects track working directories and associated sessions for easy context preservation.
- **[running-tasks.md](../../../_clones/goose/documentation/docs/guides/running-tasks.md):** Details how to pass files and instructions to the `goose run` command to execute tasks and workflows.
- **[smart-context-management.md](../../../_clones/goose/documentation/docs/guides/smart-context-management.md):** Explains how Goose handles context and conversation limits with features like summarization and truncation.
- **[tips.md](../../../_clones/goose/documentation/docs/guides/tips.md):** Provides quick tips for getting the most out of Goose, including LLM choice, session management, and extension usage.
- **[updating-goose.md](../../../_clones/goose/documentation/docs/guides/updating-goose.md):** Instructions for updating the Goose CLI and desktop apps to newer versions.
- **[using-goosehints.md](../../../_clones/goose/documentation/docs/guides/using-goosehints.md):** Explains how to use `.goosehints` files to provide additional context and instructions to Goose.
- **[using-gooseignore.md](../../../_clones/goose/documentation/docs/guides/using-gooseignore.md):** Describes how to use `.gooseignore` files to prevent Goose from accessing specific files and directories.

## documentation/docs/guides/managing-tools

- **[adjust-tool-output.md](../../../_clones/goose/documentation/docs/guides/managing-tools/adjust-tool-output.md):** Explains how to configure the verbosity of tool output and set tool timeouts.
- **[index.md](../../../_clones/goose/documentation/docs/guides/managing-tools/index.md):** An overview of managing tools in Goose, including permissions, selection strategy, and output.
- **[tool-permissions.md](../../../_clones/goose/documentation/docs/guides/managing-tools/tool-permissions.md):** Explains Goose's tool permission modes for controlling which tools it can use and how.
- **[tool-router.md](../../../_clones/goose/documentation/docs/guides/managing-tools/tool-router.md):** Describes how to configure smart tool selection strategies to improve performance with multiple extensions.

## documentation/docs/guides/recipes

- **[index.md](../../../_clones/goose/documentation/docs/guides/recipes/index.md):** An overview of recipes in Goose, including documentation, tools, and featured blog posts.
- **[recipe-reference.md](../../../_clones/goose/documentation/docs/guides/recipes/recipe-reference.md):** A complete technical reference for creating and customizing recipes in Goose via the CLI.
- **[session-recipes.md](../../../_clones/goose/documentation/docs/guides/recipes/session-recipes.md):** Explains how to create, edit, use, share, and schedule recipes from Goose sessions.
- **[storing-recipes.md](../../../_clones/goose/documentation/docs/guides/recipes/storing-recipes.md):** Covers storing, organizing, and finding Goose recipes in global and local locations.
- **[sub-recipes.md](../../../_clones/goose/documentation/docs/guides/recipes/sub-recipes.md):** Explains how recipes can use sub-recipes to perform specific tasks, enabling multi-step workflows.

## documentation/docs/mcp

- **[agentql-mcp.md](../../../_clones/goose/documentation/docs/mcp/agentql-mcp.md):** Explains how to add the AgentQL MCP Server as a Goose Extension for extracting and transforming unstructured web content.
- **[alby-mcp.md](../../../_clones/goose/documentation/docs/mcp/alby-mcp.md):** Covers adding the Alby Bitcoin Payments MCP Server as a Goose extension for lightning wallet interactions.
- **[asana-mcp.md](../../../_clones/goose/documentation/docs/mcp/asana-mcp.md):** Explains how to add the Asana MCP Server as a Goose Extension for task automation and project tracking.
- **[blender-mcp.md](../../../_clones/goose/documentation/docs/mcp/blender-mcp.md):** Covers adding the Blender MCP Server as a Goose Extension for creating 3D scenes and controlling Blender.
- **[brave-mcp.md](../../../_clones/goose/documentation/docs/mcp/brave-mcp.md):** Explains how to add the Brave Search MCP Server as a Goose Extension for interactive web and local searches.
- **[browserbase-mcp.md](../../../_clones/goose/documentation/docs/mcp/browserbase-mcp.md):** Covers adding the Browserbase MCP Server as a Goose Extension for web automation.
- **[cloudflare-mcp.md](../../../_clones/goose/documentation/docs/mcp/cloudflare-mcp.md):** Explains how to add Cloudflare's MCP Servers as Goose Extensions for managing Cloudflare infrastructure.
- **[cloudinary-asset-management-mcp.md](../../../_clones/goose/documentation/docs/mcp/cloudinary-asset-management-mcp.md):** Covers adding the Cloudinary Asset Management MCP Server as a Goose Extension for image processing workflows.
- **[cognee-mcp.md](../../../_clones/goose/documentation/docs/mcp/cognee-mcp.md):** Explains how to add the Cognee MCP Server as a Goose Extension for knowledge graph memory capabilities.
- **[computer-controller-mcp.md](../../../_clones/goose/documentation/docs/mcp/computer-controller-mcp.md):** Covers enabling and using the Computer Controller MCP Server, a built-in Goose extension for automating computer tasks.
- **[developer-mcp.md](../../../_clones/goose/documentation/docs/mcp/developer-mcp.md):** Explains how to use the Developer MCP Server as a Goose Extension for developer-centric tasks.
- **[dev.to-mcp.md](../../../_clones/goose/documentation/docs/mcp/dev.to-mcp.md):** Covers adding the Dev.to MCP Server as a Goose Extension for accessing the Dev.to public API.
- **[elevenlabs-mcp.md](../../../_clones/goose/documentation/docs/mcp/elevenlabs-mcp.md):** Explains how to add the ElevenLabs MCP Server as a Goose extension for AI-powered voice generation.
- **[fetch-mcp.md](../../../_clones/goose/documentation/docs/mcp/fetch-mcp.md):** Covers adding the Fetch MCP Server as a Goose Extension for retrieving and processing web content.
- **[figma-mcp.md](../../../_clones/goose/documentation/docs/mcp/figma-mcp.md):** Explains how to add the Figma MCP Server as a Goose Extension for interacting with Figma files.
- **[filesystem-mcp.md](../../../_clones/goose/documentation/docs/mcp/filesystem-mcp.md):** Covers adding the Filesystem MCP Server as a Goose extension for code analysis and file management.
- **[github-mcp.md](../../../_clones/goose/documentation/docs/mcp/github-mcp.md):** Explains how to add the GitHub MCP Server as a Goose Extension for file operations and repository management.
- **[gitmcp-mcp.md](../../../_clones/goose/documentation/docs/mcp/gitmcp-mcp.md):** Covers adding the Git MCP Server as a Goose Extension for live access to GitHub repos.
- **[google-drive-mcp.md](../../../_clones/goose/documentation/docs/mcp/google-drive-mcp.md):** Explains how to add the Google Drive MCP Server as a Goose extension for listing, reading, and searching files in Google Drive.
- **[google-maps-mcp.md](../../../_clones/goose/documentation/docs/mcp/google-maps-mcp.md):** Covers adding the Google Maps MCP Server as a Goose extension for geocoding, place searching, and directions.
- **[goose-docs-mcp.md](../../../_clones/goose/documentation/docs/mcp/goose-docs-mcp.md):** Explains how to add the Goose Docs MCP Server as a Goose extension for answering questions about Goose itself.
- **[jetbrains-mcp.md](../../../_clones/goose/documentation/docs/mcp/jetbrains-mcp.md):** Covers using the JetBrains MCP Server as a Goose Extension for integrating with JetBrains IDEs.
- **[knowledge-graph-mcp.md](../../../_clones/goose/documentation/docs/mcp/knowledge-graph-mcp.md):** Explains how to add the Knowledge Graph Memory MCP Server as a Goose Extension for analyzing relationships and detecting patterns.
- **[mbot-mcp.md](../../../_clones/goose/documentation/docs/mcp/mbot-mcp.md):** Covers adding the mbot MCP Server as a Goose Extension for controlling a MakeBlock mbot2 rover.
- **[memory-mcp.md](../../../_clones/goose/documentation/docs/mcp/memory-mcp.md):** Explains how to use the Memory MCP Server as a Goose Extension for storing and recalling personalized information.
- **[mongodb-mcp.md](../../../_clones/goose/documentation/docs/mcp/mongodb-mcp.md):** Covers adding the MongoDB MCP Server as a Goose Extension for interacting with MongoDB databases.
- **[neon-mcp.md](../../../_clones/goose/documentation/docs/mcp/neon-mcp.md):** Explains how to add the Neon MCP Server as a Goose Extension for interacting with Neon Postgres databases.
- **[nostrbook-mcp.md](../../../_clones/goose/documentation/docs/mcp/nostrbook-mcp.md):** Covers adding the Nostrbook MCP Server as a Goose Extension for accessing Nostr documentation.
- **[pdf-mcp.md](../../../_clones/goose/documentation/docs/mcp/pdf-mcp.md):** Explains how to add the PDF Reader MCP Server as a Goose Extension for reading and extracting text from PDFs.
- **[pieces-mcp.md](../../../_clones/goose/documentation/docs/mcp/pieces-mcp.md):** Covers adding the Pieces for Developers MCP Server as a Goose Extension for interacting with Pieces Long-Term Memory.
- **[playwright-mcp.md](../../../_clones/goose/documentation/docs/mcp/playwright-mcp.md):** Explains how to add the Playwright MCP Server as a Goose Extension for cross-browser testing and web automation.
- **[postgres-mcp.md](../../../_clones/goose/documentation/docs/mcp/postgres-mcp.md):** Covers adding the PostgreSQL MCP Server as a Goose Extension for interacting with PostgreSQL databases.
- **[reddit-mcp.md](../../../_clones/goose/documentation/docs/mcp/reddit-mcp.md):** Explains how to add the Reddit MCP Server as a Goose Extension for fetching and analyzing Reddit content.
- **[repomix-mcp.md](../../../_clones/goose/documentation/docs/mcp/repomix-mcp.md):** Covers adding the Repomix MCP Server as a Goose Extension for automated repository packing and codebase analysis.
- **[selenium-mcp.md](../../../_clones/goose/documentation/docs/mcp/selenium-mcp.md):** Explains how to add the Selenium MCP Server as a Goose Extension for automating browser interactions.
- **[speech-mcp.md](../../../_clones/goose/documentation/docs/mcp/speech-mcp.md):** Covers adding the Speech MCP Server as a Goose Extension for real-time voice interaction.
- **[square-mcp.md](../../../_clones/goose/documentation/docs/mcp/square-mcp.md):** Explains how to add the Square API as a Goose Extension for interactive and automated work with Square seller accounts.
- **[tavily-mcp.md](../../../_clones/goose/documentation/docs/mcp/tavily-mcp.md):** Covers adding the Tavily Web Search MCP Server as a Goose Extension for AI-powered web search.
- **[tutorial-mcp.md](../../../_clones/goose/documentation/docs/mcp/tutorial-mcp.md):** Explains how to use Goose's built-in Tutorial extension for guided learning.
- **[vs-code-mcp.md](../../../_clones/goose/documentation/docs/mcp/vs-code-mcp.md):** Covers adding the VS Code MCP Server as a Goose Extension for VS Code integration and file operations.
- **[youtube-transcript-mcp.md](../../../_clones/goose/documentation/docs/mcp/youtube-transcript-mcp.md):** Explains how to add the YouTube Transcript MCP Server as a Goose Extension for accessing YouTube video transcripts.

## documentation/src/pages

- **[grants.md](../../../_clones/goose/documentation/src/pages/grants.md):** Information about the Goose grant program for open-source developers building agentic AI.
- **[markdown-page.md](../../../_clones/goose/documentation/src/pages/markdown-page.md):** A simple example of a standalone Markdown page.
- **[community/data/README.md](../../../_clones/goose/documentation/src/pages/community/data/README.md):** A guide for updating the Community All Stars section on the community page.

## scripts

- **[README.md](../../../_clones/goose/scripts/README.md):** Describes the Goose Benchmark Scripts for running and analyzing Goose benchmarks.

## ui/desktop

- **[CLAUDE.md](../../../_clones/goose/ui/desktop/CLAUDE.md):** Provides guidance to Claude Code when working with the Goose desktop repository, including common development commands and architecture overview.
- **[README.md](../../../_clones/goose/ui/desktop/README.md):** The README for the Goose Desktop App, covering building, running, and platform-specific requirements.
- **[scripts/README.md](../../../_clones/goose/ui/desktop/scripts/README.md):** Describes the `goosey` script for launching the Goose GUI and `unregister-deeplink-protocols.js` for macOS.
- **[src/platform/windows/bin/README.md](../../../_clones/goose/ui/desktop/src/platform/windows/bin/README.md):** Describes Windows-specific binaries and scripts included during Windows builds.
