[package]
name = "aws-rs"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0.86"
async-trait = "0.1.87"
aws-config = { version = "1.5.18", features = ["behavior-version-latest"] }
aws-sdk-bedrockruntime = "1.76.0"
aws-sdk-sagemakerruntime = "1.63.0"
aws-smithy-types = "1.2.13"
base64 = "0.22.1"
futures = "0.3.31"
http = "1.2.0"
lazy_static = "1.5.0"
log = "0.4.22"
regex = "1.10.5"
serde = { version = "1.0.209", features = ["derive"] }
serde_json = "1.0.125"
thiserror = "1.0.63"
tokio = { version = "1.39.2", features = ["full"] }


