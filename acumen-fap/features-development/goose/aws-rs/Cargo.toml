[package]
name = "aws-rs"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0.86"
async-trait = "0.1.87"
aws-config = { version = "1.5.18", features = ["behavior-version-latest"] }
aws-sdk-bedrockruntime = "1.76.0"
aws-sdk-sagemakerruntime = "1.63.0"
aws-smithy-types = "1.2.13"
base64 = "0.22.1"
futures = "0.3.31"
http = "1.2.0"
lazy_static = "1.5.0"
log = "0.4.22"
regex = "1.10.5"
serde = { version = "1.0.209", features = ["derive"] }
serde_json = "1.0.125"
thiserror = "1.0.63"
tokio = { version = "1.39.2", features = ["full"] }

aws-config = "1.5.18"
aws-sdk-bedrockruntime = "1.76.0"
aws-sdk-dynamodb = "1.67.0"
aws-sdk-sagemakerruntime = "1.63.0"
aws-sdk-sso = "1.61.0"
aws-sdk-ssooidc = "1.62.0"
aws-sdk-sts = "1.62.0"
aws-sigv4 = "1.2.9"
aws-smithy-async = "1.2.4"
aws-smithy-eventstream = "0.60.7"
aws-smithy-http = "0.61.1"
aws-smithy-json = "0.61.2"
aws-smithy-query = "0.60.7"
aws-smithy-runtime = "1.7.8"
aws-smithy-runtime-api = "1.7.3"
aws-smithy-types = "1.2.13"
aws-smithy-xml = "0.60.9"
aws-types = "1.3.5"
