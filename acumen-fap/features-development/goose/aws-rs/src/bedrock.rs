use anyhow::{anyhow, bail, Result};
use aws_sdk_bedrockruntime::{types as bedrock, Client};
use rmcp::model::message::{Message, MessageContent, Role};
use rmcp::model::tool::{Content, Tool, Tool<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tool<PERSON>seResult};
use rmcp::model::usage::Usage;
use serde_json::Value;

// Re-export the error type
pub use crate::errors::ProviderError;

#[derive(Debug, Clone)]
pub struct BedrockClient {
    client: Client,
    model_id: String,
}

impl BedrockClient {
    pub fn new(client: Client, model_id: &str) -> Self {
        Self { client, model_id: model_id.to_string() }
    }

    pub async fn converse(
        &self,
        system: &str,
        messages: &[Message],
        tools: &[Tool],
    ) -> Result<(bedrock::Message, Option<bedrock::TokenUsage>), ProviderError> {
        let mut request = self
            .client
            .converse()
            .system(bedrock::SystemContentBlock::Text(system.to_string()))
            .model_id(&self.model_id)
            .set_messages(Some(
                messages
                    .iter()
                    .map(to_bedrock_message)
                    .collect::<Result<_>>()?,
            ));

        if !tools.is_empty() {
            request = request.tool_config(to_bedrock_tool_config(tools)?);
        }

        let response = request.send().await.map_err(|err| {
            ProviderError::ExecutionError(format!("Failed to call Bedrock: {:?}", err))
        })?;

        match response.output {
            Some(bedrock::ConverseOutput::Message(message)) => Ok((message, response.usage)),
            _ => Err(ProviderError::RequestFailed(
                "No output from Bedrock".to_string(),
            )),
        }
    }
}

pub fn to_bedrock_message(message: &Message) -> Result<bedrock::Message> {
    bedrock::Message::builder()
        .role(to_bedrock_role(&message.role))
        .set_content(Some(
            message
                .content
                .iter()
                .map(to_bedrock_message_content)
                .collect::<Result<_>>()?,
        ))
        .build()
        .map_err(|err| anyhow!("Failed to construct Bedrock message: {}", err))
}

pub fn to_bedrock_message_content(content: &MessageContent) -> Result<bedrock::ContentBlock> {
    Ok(match content {
        MessageContent::Text(text) => bedrock::ContentBlock::Text(text.text.to_string()),
        MessageContent::Image(image) => {
            bedrock::ContentBlock::Image(to_bedrock_image(&image.data, &image.mime_type)?)
        }
        MessageContent::ToolRequest(tool_req) => {
            let tool_use = match &tool_req.call {
                ToolUse::Function { .. } => {
                    let mut builder = bedrock::ToolUseBlock::builder()
                        .tool_use_id(&tool_req.id)
                        .name(&tool_req.name);
                    if let ToolUse::Function { arguments } = &tool_req.call {
                        builder = builder.input(to_bedrock_json(arguments));
                    }
                    builder.build().unwrap()
                }
                ToolUse::Error(ToolUseError { message, .. }) => {
                    let mut builder = bedrock::ToolUseBlock::builder()
                        .tool_use_id(&tool_req.id)
                        .name(&tool_req.name);
                    if let ToolUse::Error { .. } = &tool_req.call {
                        builder = builder.input(to_bedrock_json(&serde_json::json!({
                            "error": message
                        })));
                    }
                    builder.build().unwrap()
                }
            };
            bedrock::ContentBlock::ToolUse(tool_use)
        }
        MessageContent::ToolResponse(tool_res) => {
            let content_blocks = tool_res
                .result
                .content
                .iter()
                .map(|c| to_bedrock_tool_result_content_block(&tool_res.id, c.clone()))
                .collect::<Result<Vec<_>>>()?;

            bedrock::ContentBlock::ToolResult(
                bedrock::ToolResultBlock::builder()
                    .tool_use_id(&tool_res.id)
                    .set_content(Some(content_blocks))
                    .status(match tool_res.result {
                        ToolUseResult::Success { .. } => bedrock::ToolResultStatus::Success,
                        ToolUseResult::Error { .. } => bedrock::ToolResultStatus::Error,
                    })
                    .build()
                    .unwrap(),
            )
        }
        _ => bedrock::ContentBlock::Text("".to_string()),
    })
}

pub fn to_bedrock_tool_result_content_block(
    tool_use_id: &str,
    content: Content,
) -> Result<bedrock::ToolResultContentBlock> {
    Ok(match content.content {
        RawContent::Text(text) => bedrock::ToolResultContentBlock::Text(text.text),
        RawContent::Image(image) => {
            bedrock::ToolResultContentBlock::Image(to_bedrock_image(&image.data, &image.mime_type)?)
        }
        _ => bail!("Unsupported content type for Bedrock tool result"),
    })
}

pub fn to_bedrock_role(role: &Role) -> bedrock::ConversationRole {
    match role {
        Role::User => bedrock::ConversationRole::User,
        Role::Assistant => bedrock::ConversationRole::Assistant,
    }
}

pub fn to_bedrock_image(data: &String, mime_type: &String) -> Result<bedrock::ImageBlock> {
    let format = match mime_type.as_str() {
        "image/png" => bedrock::ImageFormat::Png,
        "image/jpeg" | "image/jpg" => bedrock::ImageFormat::Jpeg,
        "image/gif" => bedrock::ImageFormat::Gif,
        "image/webp" => bedrock::ImageFormat::Webp,
        _ => bail!(
            "Unsupported image format: {}. Bedrock supports png, jpeg, gif, webp",
            mime_type
        ),
    };

    let decoded_data = base64::Engine::decode(&base64::engine::general_purpose::STANDARD, data)?;
    let source = bedrock::ImageSource::Bytes(aws_smithy_types::Blob::new(decoded_data));

    Ok(bedrock::ImageBlock::builder()
        .format(format)
        .source(source)
        .build()
        .unwrap())
}

pub fn to_bedrock_tool_config(tools: &[Tool]) -> Result<bedrock::ToolConfiguration> {
    Ok(bedrock::ToolConfiguration::builder()
        .set_tools(Some(
            tools.iter().map(to_bedrock_tool).collect::<Result<_>>()?,
        ))
        .build()
        .unwrap())
}

pub fn to_bedrock_tool(tool: &Tool) -> Result<bedrock::Tool> {
    Ok(bedrock::Tool::ToolSpec(
        bedrock::ToolSpecification::builder()
            .name(&tool.name)
            .description(tool.description.clone().unwrap_or_default())
            .set_input_schema(Some(bedrock::ToolInputSchema::Json(to_bedrock_json(
                &tool.schema,
            ))))
            .build()
            .unwrap(),
    ))
}

pub fn to_bedrock_json(value: &Value) -> bedrock::Document {
    match value {
        Value::Null => bedrock::Document::Null,
        Value::Bool(b) => bedrock::Document::Bool(*b),
        Value::Number(n) => bedrock::Document::Number(n.clone()),
        Value::String(s) => bedrock::Document::String(s.clone()),
        Value::Array(arr) => bedrock::Document::Array(arr.iter().map(to_bedrock_json).collect()),
        Value::Object(obj) => bedrock::Document::Object(
            obj.iter()
                .map(|(key, val)| (key.to_string(), to_bedrock_json(val)))
                .collect(),
        ),
    }
}

pub fn from_bedrock_message(message: &bedrock::Message) -> Result<Message> {
    let role = from_bedrock_role(message.role())?;
    let content = message
        .content()
        .iter()
        .map(from_bedrock_content_block)
        .collect::<Result<Vec<_>>>()?;

    Ok(Message {
        role,
        content,
        ..Default::default()
    })
}

pub fn from_bedrock_content_block(block: &bedrock::ContentBlock) -> Result<MessageContent> {
    Ok(match block {
        bedrock::ContentBlock::Text(text) => MessageContent::text(text),
        bedrock::ContentBlock::ToolUse(tool_use) => MessageContent::tool_request(
            tool_use.tool_use_id().unwrap_or_default().to_string(),
            tool_use.name().unwrap_or_default().to_string(),
            from_bedrock_json(tool_use.input().unwrap_or(&bedrock::Document::Object(Default::default())))?,
        ),
        bedrock::ContentBlock::ToolResult(tool_res) => MessageContent::tool_response(
            tool_res.tool_use_id().unwrap_or_default().to_string(),
            if tool_res.content().is_empty() {
                bail!("Empty content for tool use from Bedrock");
            }
            else {
                let content = tool_res
                    .content()
                    .iter()
                    .map(from_bedrock_tool_result_content_block)
                    .collect::<Result<Vec<_>>>()?;
                ToolUseResult::success(content)
            }
        ),
        _ => bail!("Unsupported content block type from Bedrock"),
    })
}

pub fn from_bedrock_tool_result_content_block(
    content: &bedrock::ToolResultContentBlock,
) -> Result<Content> {
    Ok(match content {
        bedrock::ToolResultContentBlock::Text(text) => Content::text(text.to_string()),
        _ => {
            bail!("Unsupported tool result from Bedrock");
        }
    })
}

pub fn from_bedrock_role(role: &bedrock::ConversationRole) -> Result<Role> {
    match role {
        bedrock::ConversationRole::User => Ok(Role::User),
        bedrock::ConversationRole::Assistant => Ok(Role::Assistant),
        _ => bail!("Unknown role from Bedrock"),
    }
}

pub fn from_bedrock_usage(usage: &bedrock::TokenUsage) -> Usage {
    Usage {
        input_tokens: usage.input_tokens as u32,
        output_tokens: usage.output_tokens as u32,
        ..Default::default()
    }
}

pub fn from_bedrock_json(document: &bedrock::Document) -> Result<Value> {
    Ok(match document {
        bedrock::Document::Null => Value::Null,
        bedrock::Document::Bool(b) => Value::Bool(*b),
        bedrock::Document::Number(n) => Value::Number(n.clone()),
        bedrock::Document::String(s) => Value::String(s.clone()),
        bedrock::Document::Array(arr) => {
            Value::Array(arr.iter().map(from_bedrock_json).collect::<Result<_>>()?)
        }
        bedrock::Document::Object(obj) => Value::Object(
            obj.iter()
                .map(|(key, val)| Ok((key.clone(), from_bedrock_json(val)?)))
                .collect::<Result<_>>()?,
        ),
    })
}