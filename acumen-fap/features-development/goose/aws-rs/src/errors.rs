use thiserror::Error;

#[derive(Erro<PERSON>, Debug)]
pub enum ProviderError {
    #[error("Authentication failed: {0}")]
    Authentication(String),
    #[error("Request failed: {0}")]
    RequestFailed(String),
    #[error("Context length exceeded: {0}")]
    ContextLengthExceeded(String),
    #[error("Rate limit exceeded: {0}")]
    RateLimitExceeded(String),
    #[error("Server error: {0}")]
    ServerError(String),
    #[error("Execution error: {0}")]
    ExecutionError(String),
}
