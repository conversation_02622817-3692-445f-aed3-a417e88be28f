use anyhow::{Context, Result};
use arrow::record_batch::RecordBatch;
use arrow::datatypes::Schema;
use futures::TryStreamExt;
use lancedb::connection::Connection;
use lancedb::query::{ExecutableQuery, QueryBase};
use std::path::Path;
use std::sync::Arc;
use tokio::sync::RwLock;

/// A trait for types that can be stored and retrieved from the vector database.
pub trait VectorRecord: Send + Sync {
    /// Returns the Arrow schema for this record type.
    fn schema() -> Arc<Schema>;

    /// Converts a slice of records into an Arrow RecordBatch.
    fn to_batch(records: &[Self]) -> Result<RecordBatch>
    where
        Self: Sized;

    /// Converts an Arrow RecordBatch into a Vec of records.
    fn from_batch(batch: &RecordBatch) -> Result<Vec<Self>>
    where
        Self: Sized;
}

pub struct VectorDB<T: VectorRecord> {
    connection: Arc<RwLock<Connection>>,
    table_name: String,
    _marker: std::marker::PhantomData<T>,
}

impl<T: VectorRecord + 'static> VectorDB<T> {
    pub async fn new(db_path: &Path, table_name: &str) -> Result<Self> {
        if let Some(parent) = db_path.parent() {
            tokio::fs::create_dir_all(parent)
                .await
                .context("Failed to create database directory")?;
        }

        let connection = lancedb::connect(db_path.to_str().unwrap())
            .execute()
            .await
            .context("Failed to connect to LanceDB")?;

        let db = Self {
            connection: Arc::new(RwLock::new(connection)),
            table_name: table_name.to_string(),
            _marker: std::marker::PhantomData,
        };

        db.init_table().await?;
        Ok(db)
    }

    async fn init_table(&self) -> Result<()> {
        let connection = self.connection.read().await;
        let table_names = connection.table_names().execute().await?;

        if !table_names.contains(&self.table_name) {
            let schema = T::schema();
            let reader = arrow::record_batch::RecordBatchIterator::new(
                vec![RecordBatch::new_empty(schema.clone())].into_iter().map(Ok),
                schema.clone(),
            );

            drop(connection); // Release read lock
            let mut connection_write = self.connection.write().await;
            connection_write
                .create_table(&self.table_name, Box::new(reader))
                .execute()
                .await?;
        }
        Ok(())
    }

    pub async fn add(&self, records: &[T]) -> Result<()> {
        if records.is_empty() {
            return Ok(());
        }
        let batch = T::to_batch(records)?;
        let reader = arrow::record_batch::RecordBatchIterator::new(
            vec![Ok(batch)].into_iter(),
            T::schema(),
        );

        let connection = self.connection.read().await;
        let table = connection.open_table(&self.table_name).execute().await?;
        table.add(Box::new(reader)).execute().await?;
        Ok(())
    }

    pub async fn search(&self, query_vector: Vec<f32>, k: usize) -> Result<Vec<T>> {
        let connection = self.connection.read().await;
        let table = connection.open_table(&self.table_name).execute().await?;

        let results = table
            .vector_search(query_vector)?
            .limit(k)
            .execute()
            .await?;

        let batches: Vec<RecordBatch> = results.try_collect().await?;
        let mut records = Vec::new();
        for batch in batches {
            records.extend(T::from_batch(&batch)?);
        }
        Ok(records)
    }

    pub async fn delete(&self, where_clause: &str) -> Result<()> {
        let connection = self.connection.read().await;
        let table = connection.open_table(&self.table_name).execute().await?;
        table.delete(where_clause).await?;
        Ok(())
    }

    #[cfg(test)]
    pub async fn clear_table(&self) -> Result<()> {
        self.delete("1=1").await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use arrow::array::{Float32Builder, StringBuilder};
    use arrow::datatypes::{DataType, Field};

    struct TestRecord {
        id: i32,
        vector: Vec<f32>,
    }

    impl VectorRecord for TestRecord {
        fn schema() -> Arc<Schema> {
            Arc::new(Schema::new(vec![
                Field::new("id", DataType::Int32, false),
                Field::new(
                    "vector",
                    DataType::FixedSizeList(Arc::new(Field::new("item", DataType::Float32, true)), 2),
                    false,
                ),
            ]))
        }

        fn to_batch(records: &[Self]) -> Result<RecordBatch> {
            let mut id_builder = arrow::array::Int32Builder::new();
            let mut vector_builder = arrow::array::FixedSizeListBuilder::new(Float32Builder::new(), 2);

            for record in records {
                id_builder.append_value(record.id);
                vector_builder.values().append_slice(&record.vector);
                vector_builder.append(true);
            }

            let batch = RecordBatch::try_new(
                Self::schema(),
                vec![
                    Arc::new(id_builder.finish()),
                    Arc::new(vector_builder.finish()),
                ],
            )?;
            Ok(batch)
        }

        fn from_batch(batch: &RecordBatch) -> Result<Vec<Self>> {
            let ids = batch
                .column(0)
                .as_any()
                .downcast_ref::<arrow::array::Int32Array>()
                .unwrap();
            let vectors = batch
                .column(1)
                .as_any()
                .downcast_ref::<arrow::array::FixedSizeListArray>()
                .unwrap();

            let mut records = Vec::new();
            for i in 0..batch.num_rows() {
                let vector = vectors.value(i);
                let float_array = vector.as_any().downcast_ref::<arrow::array::Float32Array>().unwrap();
                records.push(TestRecord {
                    id: ids.value(i),
                    vector: float_array.values().to_vec(),
                });
            }
            Ok(records)
        }
    }

    #[tokio::test]
    async fn test_db_operations() -> Result<()> {
        let db_path = std::env::temp_dir().join("test_db");
        let db: VectorDB<TestRecord> = VectorDB::new(&db_path, "test_table").await?;

        db.clear_table().await?;

        let records = vec![
            TestRecord { id: 1, vector: vec![0.1, 0.2] },
            TestRecord { id: 2, vector: vec![0.3, 0.4] },
        ];
        db.add(&records).await?;

        let results = db.search(vec![0.1, 0.2], 1).await?;
        assert_eq!(results.len(), 1);
        assert_eq!(results[0].id, 1);

        db.delete("id = 2").await?;
        let results = db.search(vec![0.3, 0.4], 1).await?;
        assert_eq!(results.len(), 0);

        Ok(())
    }
}