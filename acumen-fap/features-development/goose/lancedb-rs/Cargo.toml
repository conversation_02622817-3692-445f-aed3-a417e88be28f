[package]
name = "lancedb-rs"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0.86"
arrow = { version = "52.2.0", features = ["full"] }
chrono = "0.4.39"
etcetera = "0.8.0"
futures = "0.3.31"
serde = { version = "1.0.209", features = ["derive"] }
tokio = { version = "1.39.2", features = ["full"] }
uuid = { version = "1.10.0", features = ["v4"] }

lancedb = "0.4.0"
lance = "0.11.1"
lance-arrow = "0.11.1"
lance-core = "0.11.1"
lance-datafusion = "0.11.1"
lance-encoding = "0.11.1"
lance-file = "0.11.1"
lance-index = "0.11.1"
lance-io = "0.11.1"
lance-linalg = "0.11.1"
lance-table = "0.11.1"
lance-testing = "0.11.1"
