[package]
name = "lancedb-rs"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0.86"
arrow = "52.2.0"
chrono = "0.4.39"
etcetera = "0.8.0"
futures = "0.3.31"
serde = { version = "1.0.209", features = ["derive"] }
tokio = { version = "1.39.2", features = ["full"] }
uuid = { version = "1.10.0", features = ["v4"] }

lancedb = "0.21.2"
lance = "0.32.0"
lance-arrow = "0.32.0"
lance-core = "0.32.0"
lance-datafusion = "0.32.0"
lance-encoding = "0.32.0"
lance-file = "0.32.0"
lance-index = "0.32.0"
lance-io = "0.32.0"
lance-linalg = "0.32.0"
lance-table = "0.32.0"
lance-testing = "0.32.0"
