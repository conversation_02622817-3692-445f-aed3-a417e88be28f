[package]
name = "http-server"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0.86"
async-trait = "0.1.87"
axum = { version = "0.8.1", features = ["ws", "macros"] }
chrono = { version = "0.4.39", features = ["serde"] }
clap = { version = "4.5.31", features = ["derive"] }
futures = "0.3.31"
http = "1.2.0"
hyper = { version = "1.6.0", features = ["full"] }
log = "0.4.22"
mime = "0.3.17"
mime_guess = "2.0.4"
rmcp = { version = "0.4.0", features = ["schemars", "auth"] }
rust-embed = "8.6.0"
serde = { version = "1.0.209", features = ["derive"] }
serde_json = "1.0.125"
tokio = { version = "1.39.2", features = ["full"] }
tokio-stream = "0.1.15"
tokio-tungstenite = "0.23.1"
tower = "0.5.2"
tower-http = { version = "0.6.6", features = ["full"] }
tracing = "0.1.40"
tracing-subscriber = { version = "0.3.18", features = ["env-filter"] }
utoipa = { version = "4.1.0", features = ["axum_extras", "chrono"] }
uuid = { version = "1.10.0", features = ["v4"] }


