{
    "openai": {
        "name": "OpenAI",
        "description": "Use GPT-4 and other OpenAI models",
        "models": ["gpt-4o", "gpt-4-turbo","o1"],
        "required_keys": ["OPENAI_API_KEY", "OPENAI_HOST", "OPENAI_BASE_PATH"]
    },
    "anthropic": {
        "name": "Anthropic",
        "description": "Use Claude and other Anthropic models",
        "models": ["claude-3.5-sonnet-2"],
        "required_keys": ["ANTHROPIC_API_KEY", "ANTHROPIC_HOST"]
    },
    "databricks": {
        "name": "Databricks",
        "description": "Connect to LLMs via Databricks",
        "models": ["goose"],
        "required_keys": ["DATABRICKS_HOST"]
    },
    "gcp_vertex_ai": {
        "name": "GCP Vertex AI",
        "description": "Use Vertex AI platform models",
        "models": ["claude-3-5-ha<PERSON>u@20241022", "claude-3-5-sonnet@20240620", "claude-3-5-sonnet-v2@20241022", "claude-3-7-sonnet@20250219", "claude-sonnet-4@20250514", "claude-opus-4@20250514", "gemini-1.5-pro-002", "gemini-2.0-flash-001", "gemini-2.0-pro-exp-02-05", "gemini-2.5-pro-exp-03-25", "gemini-2.5-flash-preview-05-20", "gemini-2.5-pro-preview-05-06", "gemini-2.5-flash", "gemini-2.5-pro"],
        "required_keys": ["GCP_PROJECT_ID", "GCP_LOCATION"]
    },
    "google": {
        "name": "Google",
        "description": "Lorem ipsum",
        "models": ["gemini-1.5-flash"],
        "required_keys": ["GOOGLE_API_KEY"]
    },
    "groq": {
        "name": "Groq",
        "description": "Lorem ipsum",
        "models": ["llama-3.3-70b-versatile"],
        "required_keys": ["GROQ_API_KEY"]
    },
    "ollama": {
        "name": "Ollama",
        "description": "Lorem ipsum",
        "models": ["qwen2.5"],
        "required_keys": ["OLLAMA_HOST"]
    },
    "openrouter": {
        "name": "OpenRouter",
        "description": "Lorem ipsum",
        "models": [],
        "required_keys": ["OPENROUTER_API_KEY"]
    },
    "azure_openai": {
        "name": "Azure OpenAI",
        "description": "Connect to Azure OpenAI Service. If no API key is provided, Azure credential chain will be used.",
        "models": ["gpt-4o", "gpt-4o-mini"],
        "required_keys": ["AZURE_OPENAI_ENDPOINT", "AZURE_OPENAI_DEPLOYMENT_NAME"]
    },
    "aws_bedrock": {
        "name": "AWS Bedrock",
        "description": "Connect to LLMs via AWS Bedrock",
        "models": ["us.anthropic.claude-3-7-sonnet-20250219-v1:0"],
        "required_keys": ["AWS_PROFILE"]
    },
    "xai": {
        "name": "Xai",
        "description": "Lorem ipsum",
        "models": ["grok-3"],
        "required_keys": ["XAI_API_KEY"]
    },
}
