# Phase 3: fap:// Protocol Implementation

## Goal
Implement custom `fap://` protocol for seamless access to local runtime resources across all platforms.

## Technical Requirements
- **URL Format**: `fap://runtime/core/fap-core.js`
- **Resolution**: Maps to `~/.fap/runtime/core/fap-core.js`
- **Fallback**: Graceful degradation to HTTP when unavailable
- **Security**: Sandboxed access, no arbitrary file reading

## Protocol Design

### 3.1 URL Structure
```
fap://runtime/core/fap-core.js          # Core runtime files
fap://components/ui/button/v1.2.0.js    # Versioned components
fap://modules/crypto/latest.js          # Latest version modules
fap://apps/myapp/index.html             # Installed applications
```

### 3.2 Platform-Specific Implementations

#### Web Platform (Service Worker) - Day 1-2
```javascript
// fap-protocol-web.js - Service Worker
self.addEventListener('fetch', event => {
    const url = new URL(event.request.url);
    
    if (url.protocol === 'fap:') {
        event.respondWith(handleFAPRequest(url));
    }
});

async function handleFAPRequest(url) {
    // Convert fap:// to local HTTP
    const localUrl = `http://localhost:8080/${url.pathname}`;
    
    try {
        const response = await fetch(localUrl);
        return response;
    } catch (error) {
        // Fallback to CDN
        const cdnUrl = `https://cdn.fap.dev/${url.pathname}`;
        return fetch(cdnUrl);
    }
}

// Register service worker
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/fap-protocol-web.js');
}
```

#### Tauri Platform (Custom Protocol) - Day 2-3
```rust
// src-tauri/src/main.rs
use tauri::Manager;

fn main() {
    tauri::Builder::default()
        .register_uri_scheme_protocol("fap", |_app, request| {
            let path = request.uri().path();
            handle_fap_request(path)
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

fn handle_fap_request(path: &str) -> Result<Response<Vec<u8>>, Box<dyn std::error::Error>> {
    use std::fs;
    use dirs::home_dir;
    
    let home = home_dir().ok_or("Cannot find home directory")?;
    let fap_root = home.join(".fap");
    let file_path = fap_root.join(path.trim_start_matches('/'));
    
    // Security: Ensure path is within .fap directory
    if !file_path.starts_with(&fap_root) {
        return Err("Access denied: path outside FAP directory".into());
    }
    
    match fs::read(&file_path) {
        Ok(content) => {
            let mime_type = get_mime_type(&file_path);
            let response = Response::builder()
                .header("Content-Type", mime_type)
                .header("Access-Control-Allow-Origin", "*")
                .body(content)?;
            Ok(response)
        }
        Err(_) => {
            // Return 404 for missing files
            let response = Response::builder()
                .status(404)
                .body(b"File not found".to_vec())?;
            Ok(response)
        }
    }
}

fn get_mime_type(path: &Path) -> &'static str {
    match path.extension().and_then(|ext| ext.to_str()) {
        Some("js") => "application/javascript",
        Some("css") => "text/css",
        Some("html") => "text/html",
        Some("json") => "application/json",
        _ => "application/octet-stream",
    }
}
```

#### Electron Platform (Custom Protocol) - Day 3
```javascript
// electron-main.js
const { protocol } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const os = require('os');

protocol.registerSchemesAsPrivileged([
    { scheme: 'fap', privileges: { secure: true, standard: true } }
]);

app.whenReady().then(() => {
    protocol.registerFileProtocol('fap', async (request, callback) => {
        const url = new URL(request.url);
        const fapRoot = path.join(os.homedir(), '.fap');
        const filePath = path.join(fapRoot, url.pathname);
        
        // Security check
        if (!filePath.startsWith(fapRoot)) {
            callback({ error: -6 }); // INVALID_ARGUMENT
            return;
        }
        
        try {
            await fs.access(filePath);
            callback({ path: filePath });
        } catch (error) {
            callback({ error: -6 }); // FILE_NOT_FOUND
        }
    });
});
```

#### P2P/Pear Platform (Custom Handler) - Day 4
```javascript
// pear-fap-protocol.js
const Pear = require('pear');
const path = require('path');
const fs = require('fs').promises;

class PearFAPProtocol {
    constructor() {
        this.fapRoot = path.join(Pear.config.storage, '.fap');
    }
    
    async resolve(fapUrl) {
        const url = new URL(fapUrl);
        const filePath = path.join(this.fapRoot, url.pathname);
        
        // Security check
        if (!filePath.startsWith(this.fapRoot)) {
            throw new Error('Access denied: path outside FAP directory');
        }
        
        try {
            const content = await fs.readFile(filePath, 'utf8');
            return content;
        } catch (error) {
            // Try P2P network fallback
            return this.fetchFromNetwork(fapUrl);
        }
    }
    
    async fetchFromNetwork(fapUrl) {
        // Use Pear's P2P capabilities to fetch from network
        const key = `fap-runtime-${fapUrl.replace('fap://', '')}`;
        return Pear.get(key);
    }
}

global.fapProtocol = new PearFAPProtocol();
```

### 3.3 Universal Protocol Handler (Day 4-5)
Client-side handler that works across all platforms:

```javascript
// fap-protocol-universal.js
class FAPProtocolHandler {
    constructor() {
        this.platform = this.detectPlatform();
        this.cache = new Map();
        this.baseLocalUrl = 'http://localhost:8080';
        this.baseCdnUrl = 'https://cdn.fap.dev';
    }
    
    detectPlatform() {
        if (typeof window.__TAURI__ !== 'undefined') return 'tauri';
        if (typeof window.electronAPI !== 'undefined') return 'electron';
        if (typeof window.Pear !== 'undefined') return 'pear';
        return 'web';
    }
    
    async resolve(fapUrl) {
        // Check cache first
        if (this.cache.has(fapUrl)) {
            return this.cache.get(fapUrl);
        }
        
        let result;
        
        switch (this.platform) {
            case 'tauri':
                result = await this.resolveTauri(fapUrl);
                break;
            case 'electron':
                result = await this.resolveElectron(fapUrl);
                break;
            case 'pear':
                result = await this.resolvePear(fapUrl);
                break;
            default:
                result = await this.resolveWeb(fapUrl);
        }
        
        // Cache successful results
        if (result) {
            this.cache.set(fapUrl, result);
        }
        
        return result;
    }
    
    async resolveTauri(fapUrl) {
        try {
            const response = await fetch(fapUrl);
            return await response.text();
        } catch (error) {
            return this.fallbackToCdn(fapUrl);
        }
    }
    
    async resolveWeb(fapUrl) {
        const localUrl = fapUrl.replace('fap://', this.baseLocalUrl + '/');
        
        try {
            const response = await fetch(localUrl);
            return await response.text();
        } catch (error) {
            return this.fallbackToCdn(fapUrl);
        }
    }
    
    async fallbackToCdn(fapUrl) {
        const cdnUrl = fapUrl.replace('fap://', this.baseCdnUrl + '/');
        
        try {
            const response = await fetch(cdnUrl);
            return await response.text();
        } catch (error) {
            throw new Error(`Failed to resolve ${fapUrl}: ${error.message}`);
        }
    }
    
    // Helper method for loading scripts
    async loadScript(fapUrl) {
        const code = await this.resolve(fapUrl);
        
        const script = document.createElement('script');
        script.textContent = code;
        document.head.appendChild(script);
        
        return script;
    }
    
    // Helper method for loading stylesheets
    async loadStylesheet(fapUrl) {
        const css = await this.resolve(fapUrl);
        
        const style = document.createElement('style');
        style.textContent = css;
        document.head.appendChild(style);
        
        return style;
    }
}

// Make available globally
window.fapProtocol = new FAPProtocolHandler();
```

### 3.4 Application Integration (Day 5)
Simple helper for apps to use fap:// protocol:

```javascript
// fap-app-helper.js
window.fap = window.fap || {};

window.fap.load = async function(resource) {
    const fapUrl = resource.startsWith('fap://') ? resource : `fap://runtime/${resource}`;
    
    if (fapUrl.endsWith('.js')) {
        return window.fapProtocol.loadScript(fapUrl);
    } else if (fapUrl.endsWith('.css')) {
        return window.fapProtocol.loadStylesheet(fapUrl);
    } else {
        return window.fapProtocol.resolve(fapUrl);
    }
};

// Usage in apps:
// await fap.load('core/fap-ui.css');
// await fap.load('components/button/latest.js');
// const data = await fap.load('data/config.json');
```

## Testing Plan
1. **Protocol Registration**: Test custom protocol registration on each platform
2. **File Resolution**: Test file path resolution and security boundaries
3. **Fallback**: Test CDN fallback when local files unavailable
4. **Cache**: Test caching behavior and invalidation
5. **MIME Types**: Test proper content-type headers

## Deliverables
- [ ] Service Worker implementation for Web
- [ ] Custom protocol handler for Tauri
- [ ] Custom protocol handler for Electron
- [ ] P2P protocol handler for Pear
- [ ] Universal protocol handler library
- [ ] Application integration helpers
- [ ] Security validation and testing

## Next Phase
Once protocol is implemented, move to `04-COMPONENTS.md` for component system design.

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"id": "1", "content": "Create 03-PROTOCOL.md - fap:// protocol design", "status": "completed"}, {"id": "2", "content": "Create 04-COMPONENTS.md - Component system design", "status": "in_progress"}, {"id": "3", "content": "Create 05-PLATFORMS.md - Cross-platform integration", "status": "pending"}, {"id": "4", "content": "Create 06-DEVELOPER.md - Development tools", "status": "pending"}, {"id": "5", "content": "Create TRACKING.md - Progress tracking", "status": "pending"}, {"id": "6", "content": "Create DECISIONS.md - Technical decisions log", "status": "pending"}]