# FAP Runtime - Implementation Overview

## Vision
Build a lightweight, offline-first runtime that enables vanilla HTML/CSS/JS applications across Web, Electron, Tauri, P2P (Pear), and CLI platforms.

## Core Principles
- **Vanilla First**: No build steps, pure web standards
- **Offline First**: Works without internet after initial setup
- **Shared Everything**: Download once, use everywhere
- **Platform Agnostic**: Same code runs on all targets

## Architecture Summary

```
FAP Runtime Bootstrap (Tauri ~5MB)
├── Downloads runtime to ~/.fap/
├── Serves local resources via fap:// protocol
├── Manages app lifecycle and updates
└── Enables cross-platform app development

User Apps (~50KB each)
├── HTML/CSS/JS only
├── Reference shared runtime via fap://
├── Work offline after first load
└── Deploy to any platform without changes
```

## Implementation Timeline
1. **Week 1-2**: Bootstrap Tauri app + local server
2. **Week 3-4**: Component system + fap:// protocol  
3. **Week 5-6**: Cross-platform adapters
4. **Week 7-8**: Developer tools + documentation

## Success Criteria
- [ ] Bootstrap under 5MB
- [ ] Apps 90%+ smaller than bundled alternatives
- [ ] Sub-500ms runtime startup
- [ ] Works completely offline
- [ ] Identical behavior across all platforms

## Plan Files Structure
- `01-BOOTSTRAP.md` - Tauri bootstrap app implementation
- `02-RUNTIME.md` - Core runtime architecture
- `03-PROTOCOL.md` - fap:// protocol design
- `04-COMPONENTS.md` - Component system design
- `05-PLATFORMS.md` - Cross-platform integration
- `06-DEVELOPER.md` - Development tools
- `TRACKING.md` - Progress tracking
- `DECISIONS.md` - Technical decisions log