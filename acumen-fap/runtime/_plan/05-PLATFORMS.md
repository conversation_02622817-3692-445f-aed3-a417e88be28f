# Phase 5: Cross-Platform Integration

## Goal
Ensure FAP Runtime works seamlessly across Web, Electron, Tauri, P2P (Pear), and CLI platforms with consistent APIs and behavior.

## Technical Requirements
- **Unified API**: Same JavaScript API across all platforms
- **Capability Detection**: Apps adapt to platform capabilities  
- **Graceful Degradation**: Features degrade gracefully on limited platforms
- **Performance**: Platform-specific optimizations

## Platform Architecture

### 5.1 Platform Detection & Capabilities (Day 1)
```javascript
// fap-platform-detector.js
class FAPPlatformDetector {
    constructor() {
        this.platform = this.detectPlatform();
        this.capabilities = this.detectCapabilities();
    }
    
    detectPlatform() {
        // Tauri detection
        if (typeof window.__TAURI__ !== 'undefined') {
            return {
                type: 'tauri',
                os: this.detectOS(),
                runtime: 'rust',
                native: true
            };
        }
        
        // Electron detection
        if (typeof window.electronAPI !== 'undefined' || 
            (typeof process !== 'undefined' && process.versions?.electron)) {
            return {
                type: 'electron',
                os: this.detectOS(),
                runtime: 'node',
                native: true
            };
        }
        
        // Pear detection
        if (typeof window.Pear !== 'undefined') {
            return {
                type: 'pear',
                os: this.detectOS(),
                runtime: 'node',
                p2p: true
            };
        }
        
        // CLI detection (Node.js environment)
        if (typeof process !== 'undefined' && process.versions?.node && !process.versions?.electron) {
            return {
                type: 'cli',
                os: this.detectOS(),
                runtime: 'node',
                headless: true
            };
        }
        
        // Web browser (default)
        return {
            type: 'web',
            os: this.detectOS(),
            runtime: 'browser',
            sandboxed: true
        };
    }
    
    detectOS() {
        if (typeof navigator !== 'undefined') {
            const platform = navigator.platform.toLowerCase();
            
            if (platform.includes('win')) return 'windows';
            if (platform.includes('mac')) return 'macos';
            if (platform.includes('linux')) return 'linux';
        }
        
        if (typeof process !== 'undefined') {
            return process.platform;
        }
        
        return 'unknown';
    }
    
    detectCapabilities() {
        const caps = {
            // File system access
            fs: false,
            fsWrite: false,
            
            // Network capabilities
            http: true,
            websocket: true,
            p2p: false,
            
            // System integration
            notifications: false,
            clipboard: false,
            systemTray: false,
            windowManagement: false,
            
            // Storage
            localStorage: typeof localStorage !== 'undefined',
            indexedDB: typeof indexedDB !== 'undefined',
            
            // Advanced features
            crypto: typeof crypto !== 'undefined',
            webgl: this.hasWebGL(),
            webassembly: typeof WebAssembly !== 'undefined',
            
            // UI capabilities
            fullscreen: document.fullscreenEnabled || false,
            pointerLock: 'pointerLockElement' in document
        };
        
        // Platform-specific capabilities
        switch (this.platform.type) {
            case 'tauri':
                caps.fs = true;
                caps.fsWrite = true;
                caps.notifications = true;
                caps.clipboard = true;
                caps.systemTray = true;
                caps.windowManagement = true;
                break;
                
            case 'electron':
                caps.fs = true;
                caps.fsWrite = true;
                caps.notifications = true;
                caps.clipboard = true;
                caps.systemTray = true;
                caps.windowManagement = true;
                break;
                
            case 'pear':
                caps.fs = true;
                caps.fsWrite = true;
                caps.p2p = true;
                break;
                
            case 'cli':
                caps.fs = true;
                caps.fsWrite = true;
                break;
        }
        
        return caps;
    }
    
    hasWebGL() {
        try {
            const canvas = document.createElement('canvas');
            return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'));
        } catch (e) {
            return false;
        }
    }
    
    canUse(capability) {
        return this.capabilities[capability] === true;
    }
}
```

### 5.2 Platform-Specific Adapters (Day 2-3)

#### File System Adapter
```javascript
// fap-fs-adapter.js
class FAPFileSystemAdapter {
    constructor(platform) {
        this.platform = platform;
        this.adapter = this.createAdapter();
    }
    
    createAdapter() {
        switch (this.platform.type) {
            case 'tauri':
                return new TauriFSAdapter();
            case 'electron':
                return new ElectronFSAdapter();
            case 'pear':
                return new PearFSAdapter();
            case 'cli':
                return new NodeFSAdapter();
            default:
                return new WebFSAdapter();
        }
    }
    
    async readFile(path) {
        return this.adapter.readFile(path);
    }
    
    async writeFile(path, content) {
        return this.adapter.writeFile(path, content);
    }
    
    async readDir(path) {
        return this.adapter.readDir(path);
    }
    
    async exists(path) {
        return this.adapter.exists(path);
    }
}

class TauriFSAdapter {
    async readFile(path) {
        const { readTextFile } = window.__TAURI__.fs;
        return readTextFile(path);
    }
    
    async writeFile(path, content) {
        const { writeTextFile } = window.__TAURI__.fs;
        return writeTextFile(path, content);
    }
    
    async readDir(path) {
        const { readDir } = window.__TAURI__.fs;
        return readDir(path);
    }
    
    async exists(path) {
        const { exists } = window.__TAURI__.fs;
        return exists(path);
    }
}

class WebFSAdapter {
    constructor() {
        this.storage = new Map();
    }
    
    async readFile(path) {
        if (!this.storage.has(path)) {
            throw new Error(`File not found: ${path}`);
        }
        return this.storage.get(path);
    }
    
    async writeFile(path, content) {
        this.storage.set(path, content);
        
        // Persist to localStorage if available
        if (typeof localStorage !== 'undefined') {
            localStorage.setItem(`fap-fs:${path}`, content);
        }
    }
    
    async readDir(path) {
        const entries = [];
        for (const [filePath] of this.storage) {
            if (filePath.startsWith(path)) {
                entries.push(filePath);
            }
        }
        return entries;
    }
    
    async exists(path) {
        return this.storage.has(path);
    }
}
```

#### Notification Adapter
```javascript
// fap-notification-adapter.js
class FAPNotificationAdapter {
    constructor(platform) {
        this.platform = platform;
        this.adapter = this.createAdapter();
    }
    
    createAdapter() {
        switch (this.platform.type) {
            case 'tauri':
                return new TauriNotificationAdapter();
            case 'electron':
                return new ElectronNotificationAdapter();
            case 'web':
                return new WebNotificationAdapter();
            default:
                return new NullNotificationAdapter();
        }
    }
    
    async show(title, options = {}) {
        return this.adapter.show(title, options);
    }
    
    async requestPermission() {
        return this.adapter.requestPermission();
    }
}

class WebNotificationAdapter {
    async requestPermission() {
        if (!('Notification' in window)) {
            return 'denied';
        }
        
        if (Notification.permission === 'granted') {
            return 'granted';
        }
        
        const permission = await Notification.requestPermission();
        return permission;
    }
    
    async show(title, options = {}) {
        const permission = await this.requestPermission();
        
        if (permission !== 'granted') {
            throw new Error('Notification permission denied');
        }
        
        return new Notification(title, options);
    }
}

class TauriNotificationAdapter {
    async show(title, options = {}) {
        const { sendNotification } = window.__TAURI__.notification;
        return sendNotification({ title, body: options.body || '', icon: options.icon });
    }
    
    async requestPermission() {
        const { requestPermission } = window.__TAURI__.notification;
        return requestPermission();
    }
}

class NullNotificationAdapter {
    async show(title, options = {}) {
        console.log(`Notification: ${title}`, options);
        return null;
    }
    
    async requestPermission() {
        return 'denied';
    }
}
```

### 5.3 Unified Platform API (Day 3-4)
```javascript
// fap-platform-api.js
class FAPPlatformAPI {
    constructor() {
        this.detector = new FAPPlatformDetector();
        this.platform = this.detector.platform;
        this.capabilities = this.detector.capabilities;
        
        // Initialize adapters
        this.fs = new FAPFileSystemAdapter(this.platform);
        this.notifications = new FAPNotificationAdapter(this.platform);
        this.clipboard = new FAPClipboardAdapter(this.platform);
        this.window = new FAPWindowAdapter(this.platform);
    }
    
    // Platform info
    getPlatform() {
        return this.platform;
    }
    
    getCapabilities() {
        return this.capabilities;
    }
    
    canUse(capability) {
        return this.detector.canUse(capability);
    }
    
    // File system operations (if supported)
    async readFile(path) {
        if (!this.canUse('fs')) {
            throw new Error('File system access not available on this platform');
        }
        return this.fs.readFile(path);
    }
    
    async writeFile(path, content) {
        if (!this.canUse('fsWrite')) {
            throw new Error('File system write access not available on this platform');
        }
        return this.fs.writeFile(path, content);
    }
    
    // Notifications (if supported)
    async showNotification(title, options = {}) {
        if (!this.canUse('notifications')) {
            console.warn('Notifications not available on this platform');
            return null;
        }
        return this.notifications.show(title, options);
    }
    
    // Clipboard operations (if supported)
    async readClipboard() {
        if (!this.canUse('clipboard')) {
            throw new Error('Clipboard access not available on this platform');
        }
        return this.clipboard.read();
    }
    
    async writeClipboard(text) {
        if (!this.canUse('clipboard')) {
            throw new Error('Clipboard access not available on this platform');
        }
        return this.clipboard.write(text);
    }
    
    // Window management (if supported)
    async minimizeWindow() {
        if (!this.canUse('windowManagement')) {
            console.warn('Window management not available on this platform');
            return;
        }
        return this.window.minimize();
    }
    
    async maximizeWindow() {
        if (!this.canUse('windowManagement')) {
            console.warn('Window management not available on this platform');
            return;
        }
        return this.window.maximize();
    }
    
    // Storage operations
    async getStorageItem(key) {
        if (this.canUse('localStorage')) {
            return localStorage.getItem(key);
        } else if (this.canUse('fs')) {
            try {
                return await this.readFile(`~/.fap/storage/${key}`);
            } catch (e) {
                return null;
            }
        } else {
            throw new Error('No storage mechanism available');
        }
    }
    
    async setStorageItem(key, value) {
        if (this.canUse('localStorage')) {
            localStorage.setItem(key, value);
        } else if (this.canUse('fsWrite')) {
            await this.writeFile(`~/.fap/storage/${key}`, value);
        } else {
            throw new Error('No storage mechanism available');
        }
    }
}
```

### 5.4 Platform-Specific Deployment (Day 4-5)

#### Web Deployment
```javascript
// web-deployment.js
class WebDeployment {
    static async initialize() {
        // Register service worker for offline support
        if ('serviceWorker' in navigator) {
            try {
                await navigator.serviceWorker.register('/fap-sw.js');
                console.log('FAP Service Worker registered');
            } catch (error) {
                console.warn('FAP Service Worker registration failed:', error);
            }
        }
        
        // Initialize FAP Runtime
        window.fap = new FAPPlatformAPI();
        await window.fap.init();
        
        // Notify app that FAP is ready
        window.dispatchEvent(new CustomEvent('fap:ready'));
    }
    
    static async deploy(appManifest) {
        // Deploy app as PWA
        const manifest = {
            name: appManifest.name,
            short_name: appManifest.shortName,
            start_url: '/',
            display: 'standalone',
            theme_color: appManifest.themeColor || '#007acc',
            background_color: appManifest.backgroundColor || '#ffffff',
            icons: appManifest.icons || []
        };
        
        // Create manifest.json
        const manifestBlob = new Blob([JSON.stringify(manifest, null, 2)], {
            type: 'application/json'
        });
        
        const manifestUrl = URL.createObjectURL(manifestBlob);
        
        const link = document.createElement('link');
        link.rel = 'manifest';
        link.href = manifestUrl;
        document.head.appendChild(link);
    }
}
```

#### Tauri Deployment
```rust
// src-tauri/src/deployment.rs
use tauri::{Manager, Window};
use std::path::PathBuf;

pub struct TauriDeployment;

impl TauriDeployment {
    pub fn initialize(app: &tauri::App) -> Result<(), Box<dyn std::error::Error>> {
        // Set up custom protocol
        let app_handle = app.handle();
        
        app.listen_global("fap-deploy-app", move |event| {
            if let Some(payload) = event.payload() {
                let manifest: AppManifest = serde_json::from_str(payload).unwrap();
                Self::deploy_app(&app_handle, manifest);
            }
        });
        
        Ok(())
    }
    
    pub fn deploy_app(app_handle: &tauri::AppHandle, manifest: AppManifest) -> Result<(), Box<dyn std::error::Error>> {
        // Create app directory
        let apps_dir = get_fap_path()?.join("apps").join(&manifest.id);
        std::fs::create_dir_all(&apps_dir)?;
        
        // Write app files
        for file in manifest.files {
            let file_path = apps_dir.join(&file.path);
            std::fs::write(file_path, file.content)?;
        }
        
        // Register app in registry
        Self::register_app(&manifest)?;
        
        Ok(())
    }
    
    fn register_app(manifest: &AppManifest) -> Result<(), Box<dyn std::error::Error>> {
        let registry_path = get_fap_path()?.join("config").join("apps.json");
        
        let mut apps: Vec<AppManifest> = if registry_path.exists() {
            let content = std::fs::read_to_string(&registry_path)?;
            serde_json::from_str(&content)?
        } else {
            Vec::new()
        };
        
        // Update or add app
        if let Some(existing) = apps.iter_mut().find(|app| app.id == manifest.id) {
            *existing = manifest.clone();
        } else {
            apps.push(manifest.clone());
        }
        
        // Save registry
        let content = serde_json::to_string_pretty(&apps)?;
        std::fs::write(registry_path, content)?;
        
        Ok(())
    }
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct AppManifest {
    pub id: String,
    pub name: String,
    pub version: String,
    pub description: String,
    pub entry_point: String,
    pub files: Vec<AppFile>,
    pub permissions: Vec<String>,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct AppFile {
    pub path: String,
    pub content: String,
}
```

### 5.5 Feature Detection Examples (Day 5)
```javascript
// Example: Feature-aware component
class FAPMediaPlayer {
    constructor(options = {}) {
        this.options = options;
        this.platform = window.fap.getPlatform();
        this.element = null;
    }
    
    render() {
        if (window.fap.canUse('webgl')) {
            return this.renderAdvanced();
        } else {
            return this.renderBasic();
        }
    }
    
    renderAdvanced() {
        // Use WebGL for advanced visualization
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl');
        
        // ... WebGL implementation
        
        return canvas;
    }
    
    renderBasic() {
        // Fallback to HTML5 video
        const video = document.createElement('video');
        video.controls = true;
        
        // ... basic implementation
        
        return video;
    }
    
    async savePlaylist() {
        if (window.fap.canUse('fsWrite')) {
            // Save to file system
            await window.fap.writeFile('~/Music/playlist.json', JSON.stringify(this.playlist));
        } else {
            // Save to localStorage
            await window.fap.setStorageItem('playlist', JSON.stringify(this.playlist));
        }
    }
}
```

## Testing Plan
1. **Platform Detection**: Test accurate platform and capability detection
2. **API Consistency**: Verify same API works across all platforms
3. **Graceful Degradation**: Test feature fallbacks
4. **Performance**: Benchmark platform-specific optimizations
5. **Deployment**: Test app deployment on each platform

## Deliverables
- [ ] Platform detection and capability system
- [ ] Unified API with platform adapters
- [ ] File system abstraction layer
- [ ] Notification system abstraction
- [ ] Platform-specific deployment tools
- [ ] Feature detection examples and patterns

## Next Phase
Once platform integration is complete, move to `06-DEVELOPER.md` for development tools.

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"id": "1", "content": "Create 03-PROTOCOL.md - fap:// protocol design", "status": "completed"}, {"id": "2", "content": "Create 04-COMPONENTS.md - Component system design", "status": "completed"}, {"id": "3", "content": "Create 05-PLATFORMS.md - Cross-platform integration", "status": "completed"}, {"id": "4", "content": "Create 06-DEVELOPER.md - Development tools", "status": "in_progress"}, {"id": "5", "content": "Create TRACKING.md - Progress tracking", "status": "pending"}, {"id": "6", "content": "Create DECISIONS.md - Technical decisions log", "status": "pending"}]