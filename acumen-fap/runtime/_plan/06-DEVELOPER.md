# Phase 6: Developer Tools & Experience

## Goal
Create comprehensive development tools that make building FAP applications fast, enjoyable, and maintainable.

## Technical Requirements
- **Zero Config**: No complex build setup required
- **Hot Reload**: Instant feedback during development
- **Debugging**: Rich debugging capabilities
- **Testing**: Built-in testing framework
- **Documentation**: Auto-generated docs

## Developer Tools Architecture

### 6.1 FAP CLI Tool (Day 1-2)
```bash
# Installation
npm install -g @fap/cli

# Commands
fap init myapp                 # Create new FAP app
fap dev                       # Start development server
fap build                     # Build for production
fap deploy web                # Deploy to web
fap deploy tauri              # Deploy to Tauri
fap component create button   # Create new component
fap component publish button  # Publish to registry
```

#### CLI Implementation
```javascript
#!/usr/bin/env node
// bin/fap-cli.js

const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs').promises;

const program = new Command();

program
  .name('fap')
  .description('FAP Runtime CLI')
  .version('1.0.0');

program
  .command('init')
  .description('Create a new FAP application')
  .argument('<name>', 'Application name')
  .option('-t, --template <template>', 'Template to use', 'basic')
  .action(async (name, options) => {
    console.log(chalk.blue(`Creating FAP app: ${name}`));
    await createApp(name, options);
  });

program
  .command('dev')
  .description('Start development server')
  .option('-p, --port <port>', 'Port to use', '3000')
  .option('--hot', 'Enable hot reloading', true)
  .action(async (options) => {
    console.log(chalk.green('Starting FAP development server...'));
    await startDevServer(options);
  });

program
  .command('build')
  .description('Build application for production')
  .option('-o, --output <dir>', 'Output directory', 'dist')
  .action(async (options) => {
    console.log(chalk.yellow('Building FAP application...'));
    await buildApp(options);
  });

async function createApp(name, options) {
  const appDir = path.resolve(name);
  await fs.mkdir(appDir, { recursive: true });
  
  // Create package.json
  const packageJson = {
    name: name,
    version: '1.0.0',
    description: 'A FAP application',
    main: 'index.html',
    scripts: {
      dev: 'fap dev',
      build: 'fap build'
    },
    fap: {
      runtime: '^1.0.0',
      template: options.template
    }
  };
  
  await fs.writeFile(
    path.join(appDir, 'package.json'),
    JSON.stringify(packageJson, null, 2)
  );
  
  // Create basic HTML
  const indexHtml = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${name}</title>
    <script src="fap://runtime/bootstrap.js"></script>
</head>
<body>
    <div id="app">
        <h1>Welcome to ${name}!</h1>
        <p>Your FAP application is ready.</p>
    </div>
    
    <script>
        document.addEventListener('fap:ready', async () => {
            console.log('FAP Runtime loaded!');
            
            // Load a component
            const { Button } = await fap.loadComponent('ui/button');
            
            const button = new Button({
                text: 'Click me!',
                variant: 'primary'
            });
            
            button.on('click', () => {
                alert('Hello from FAP!');
            });
            
            button.render(document.getElementById('app'));
        });
    </script>
</body>
</html>`;
  
  await fs.writeFile(path.join(appDir, 'index.html'), indexHtml);
  
  console.log(chalk.green(`✅ Created ${name} successfully!`));
  console.log(chalk.cyan(`cd ${name} && fap dev`));
}

async function startDevServer(options) {
  const DevServer = require('./dev-server');
  const server = new DevServer(options);
  await server.start();
}

module.exports = { program };
```

### 6.2 Development Server (Day 2-3)
```javascript
// lib/dev-server.js
const express = require('express');
const WebSocket = require('ws');
const chokidar = require('chokidar');
const path = require('path');
const chalk = require('chalk');

class FAPDevServer {
    constructor(options = {}) {
        this.options = {
            port: 3000,
            hot: true,
            ...options
        };
        
        this.app = express();
        this.wss = null;
        this.watchers = [];
    }
    
    async start() {
        // Set up Express middleware
        this.setupMiddleware();
        
        // Set up WebSocket for hot reload
        if (this.options.hot) {
            this.setupHotReload();
        }
        
        // Set up file watchers
        this.setupWatchers();
        
        // Start server
        this.server = this.app.listen(this.options.port, () => {
            console.log(chalk.green(`🚀 FAP Dev Server running on http://localhost:${this.options.port}`));
            console.log(chalk.blue('📁 Serving from current directory'));
            
            if (this.options.hot) {
                console.log(chalk.yellow('🔥 Hot reloading enabled'));
            }
        });
    }
    
    setupMiddleware() {
        // Static file serving
        this.app.use(express.static('.'));
        
        // FAP runtime proxy
        this.app.use('/fap-runtime', (req, res, next) => {
            // Serve from local FAP runtime if available, otherwise proxy to CDN
            this.proxyToRuntime(req, res, next);
        });
        
        // Development API endpoints
        this.app.get('/fap-api/components', async (req, res) => {
            const components = await this.getAvailableComponents();
            res.json(components);
        });
        
        this.app.get('/fap-api/reload', (req, res) => {
            this.broadcastReload();
            res.json({ success: true });
        });
    }
    
    setupHotReload() {
        const server = require('http').createServer();
        this.wss = new WebSocket.Server({ server });
        
        this.wss.on('connection', (ws) => {
            console.log(chalk.cyan('🔌 Hot reload client connected'));
            
            ws.on('close', () => {
                console.log(chalk.cyan('🔌 Hot reload client disconnected'));
            });
        });
        
        server.listen(this.options.port + 1);
        
        // Inject hot reload client
        this.app.use((req, res, next) => {
            if (req.path.endsWith('.html')) {
                const originalSend = res.send;
                res.send = function(html) {
                    if (typeof html === 'string') {
                        html = html.replace('</head>', `
                            <script>
                                (function() {
                                    const ws = new WebSocket('ws://localhost:${this.options.port + 1}');
                                    ws.onmessage = function(event) {
                                        const data = JSON.parse(event.data);
                                        if (data.type === 'reload') {
                                            window.location.reload();
                                        } else if (data.type === 'component-update') {
                                            window.fap?.reloadComponent?.(data.component);
                                        }
                                    };
                                })();
                            </script>
                        </head>`);
                    }
                    originalSend.call(this, html);
                }.bind(this);
            }
            next();
        });
    }
    
    setupWatchers() {
        // Watch HTML files
        const htmlWatcher = chokidar.watch('**/*.html', {
            ignored: ['node_modules/**', 'dist/**']
        });
        
        htmlWatcher.on('change', (path) => {
            console.log(chalk.blue(`📝 Changed: ${path}`));
            this.broadcastReload();
        });
        
        // Watch JavaScript files
        const jsWatcher = chokidar.watch('**/*.js', {
            ignored: ['node_modules/**', 'dist/**']
        });
        
        jsWatcher.on('change', (path) => {
            console.log(chalk.blue(`📝 Changed: ${path}`));
            if (path.includes('components/')) {
                this.broadcastComponentUpdate(path);
            } else {
                this.broadcastReload();
            }
        });
        
        // Watch CSS files
        const cssWatcher = chokidar.watch('**/*.css', {
            ignored: ['node_modules/**', 'dist/**']
        });
        
        cssWatcher.on('change', (path) => {
            console.log(chalk.blue(`🎨 Style changed: ${path}`));
            this.broadcastStyleUpdate(path);
        });
        
        this.watchers = [htmlWatcher, jsWatcher, cssWatcher];
    }
    
    broadcastReload() {
        if (this.wss) {
            this.wss.clients.forEach(client => {
                if (client.readyState === WebSocket.OPEN) {
                    client.send(JSON.stringify({ type: 'reload' }));
                }
            });
        }
    }
    
    broadcastComponentUpdate(componentPath) {
        if (this.wss) {
            const componentName = this.extractComponentName(componentPath);
            this.wss.clients.forEach(client => {
                if (client.readyState === WebSocket.OPEN) {
                    client.send(JSON.stringify({
                        type: 'component-update',
                        component: componentName,
                        path: componentPath
                    }));
                }
            });
        }
    }
    
    broadcastStyleUpdate(stylePath) {
        if (this.wss) {
            this.wss.clients.forEach(client => {
                if (client.readyState === WebSocket.OPEN) {
                    client.send(JSON.stringify({
                        type: 'style-update',
                        path: stylePath
                    }));
                }
            });
        }
    }
    
    async stop() {
        if (this.server) {
            this.server.close();
        }
        
        if (this.wss) {
            this.wss.close();
        }
        
        this.watchers.forEach(watcher => watcher.close());
    }
}

module.exports = FAPDevServer;
```

### 6.3 Component Generator (Day 3)
```javascript
// lib/component-generator.js
const fs = require('fs').promises;
const path = require('path');
const chalk = require('chalk');

class ComponentGenerator {
    async create(name, options = {}) {
        const componentDir = path.join('components', name);
        await fs.mkdir(componentDir, { recursive: true });
        
        // Generate component files
        await this.generateManifest(componentDir, name, options);
        await this.generateJS(componentDir, name, options);
        await this.generateCSS(componentDir, name, options);
        await this.generateTest(componentDir, name, options);
        await this.generateDocs(componentDir, name, options);
        
        console.log(chalk.green(`✅ Created component: ${name}`));
        console.log(chalk.cyan(`📁 Location: ${componentDir}`));
    }
    
    async generateManifest(dir, name, options) {
        const manifest = {
            name: `components/${name}`,
            version: '1.0.0',
            description: options.description || `${name} component`,
            main: 'index.js',
            style: 'style.css',
            dependencies: options.dependencies || {},
            exports: {
                default: this.pascalCase(name)
            },
            tags: options.tags || ['ui'],
            compatibility: {
                web: true,
                tauri: true,
                electron: true,
                pear: true,
                cli: false
            }
        };
        
        await fs.writeFile(
            path.join(dir, 'manifest.json'),
            JSON.stringify(manifest, null, 2)
        );
    }
    
    async generateJS(dir, name, options) {
        const className = this.pascalCase(name);
        
        const jsTemplate = `(function(exports, module, require, fap) {
    'use strict';
    
    class ${className} {
        constructor(options = {}) {
            this.options = {
                // Default options
                ...options
            };
            
            this.element = null;
            this.listeners = new Map();
        }
        
        render(container) {
            this.element = document.createElement('div');
            this.element.className = 'fap-${name}';
            
            // Add your rendering logic here
            this.element.textContent = 'Hello from ${className}!';
            
            if (container) {
                container.appendChild(this.element);
            }
            
            return this.element;
        }
        
        on(event, callback) {
            if (!this.listeners.has(event)) {
                this.listeners.set(event, new Set());
            }
            this.listeners.get(event).add(callback);
            
            return () => this.off(event, callback);
        }
        
        off(event, callback) {
            const eventListeners = this.listeners.get(event);
            if (eventListeners) {
                eventListeners.delete(callback);
            }
        }
        
        emit(event, ...args) {
            const eventListeners = this.listeners.get(event);
            if (eventListeners) {
                for (const callback of eventListeners) {
                    try {
                        callback(...args);
                    } catch (error) {
                        console.error(\`Error in \${event} listener:\`, error);
                    }
                }
            }
        }
        
        destroy() {
            if (this.element && this.element.parentNode) {
                this.element.parentNode.removeChild(this.element);
            }
            this.listeners.clear();
            this.element = null;
        }
    }
    
    // Export the component
    module.exports = { ${className} };
    
})(exports, module, require, fap);`;
        
        await fs.writeFile(path.join(dir, 'index.js'), jsTemplate);
    }
    
    async generateCSS(dir, name, options) {
        const cssTemplate = `.fap-${name} {
    /* Base styles */
    display: block;
    
    /* Use CSS custom properties for theming */
    color: var(--fap-text-color, #333);
    background: var(--fap-background-color, transparent);
    
    /* Add your styles here */
}

.fap-${name}:hover {
    /* Hover styles */
}

.fap-${name}--disabled {
    /* Disabled state */
    opacity: 0.5;
    pointer-events: none;
}`;
        
        await fs.writeFile(path.join(dir, 'style.css'), cssTemplate);
    }
    
    async generateTest(dir, name, options) {
        const className = this.pascalCase(name);
        
        const testTemplate = `// ${className} Component Tests
describe('${className}', () => {
    let component;
    let container;
    
    beforeEach(() => {
        container = document.createElement('div');
        document.body.appendChild(container);
        
        component = new ${className}();
    });
    
    afterEach(() => {
        if (component) {
            component.destroy();
        }
        if (container && container.parentNode) {
            container.parentNode.removeChild(container);
        }
    });
    
    test('should create component instance', () => {
        expect(component).toBeInstanceOf(${className});
    });
    
    test('should render element', () => {
        const element = component.render(container);
        
        expect(element).toBeDefined();
        expect(element.classList.contains('fap-${name}')).toBe(true);
        expect(container.contains(element)).toBe(true);
    });
    
    test('should handle events', () => {
        const mockCallback = jest.fn();
        
        component.on('test', mockCallback);
        component.emit('test', 'data');
        
        expect(mockCallback).toHaveBeenCalledWith('data');
    });
    
    test('should clean up on destroy', () => {
        const element = component.render(container);
        
        component.destroy();
        
        expect(container.contains(element)).toBe(false);
        expect(component.element).toBe(null);
    });
});`;
        
        await fs.writeFile(path.join(dir, 'test.js'), testTemplate);
    }
    
    async generateDocs(dir, name, options) {
        const className = this.pascalCase(name);
        
        const docsTemplate = `# ${className} Component

## Description
${options.description || `${className} component for FAP applications.`}

## Usage

\`\`\`javascript
// Load the component
const { ${className} } = await fap.loadComponent('components/${name}');

// Create instance
const ${name} = new ${className}({
    // options
});

// Render
${name}.render(document.getElementById('container'));
\`\`\`

## Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| ... | ... | ... | ... |

## Methods

### \`render(container)\`
Renders the component to the specified container.

### \`destroy()\`
Cleans up the component and removes it from the DOM.

## Events

### \`event-name\`
Description of the event.

## Styling

The component uses the following CSS classes:
- \`.fap-${name}\` - Main component class
- \`.fap-${name}--disabled\` - Disabled state

## CSS Custom Properties

| Property | Default | Description |
|----------|---------|-------------|
| \`--fap-text-color\` | \`#333\` | Text color |
| \`--fap-background-color\` | \`transparent\` | Background color |`;
        
        await fs.writeFile(path.join(dir, 'README.md'), docsTemplate);
    }
    
    pascalCase(str) {
        return str.replace(/(-|_|\s)+(.)?/g, (match, separator, chr) => {
            return chr ? chr.toUpperCase() : '';
        }).replace(/^(.)/, (match, chr) => chr.toUpperCase());
    }
}

module.exports = ComponentGenerator;
```

### 6.4 Testing Framework Integration (Day 4)
```javascript
// lib/test-runner.js
const { spawn } = require('child_process');
const path = require('path');
const chalk = require('chalk');

class FAPTestRunner {
    constructor(options = {}) {
        this.options = {
            watch: false,
            coverage: false,
            ...options
        };
    }
    
    async run() {
        console.log(chalk.blue('🧪 Running FAP tests...'));
        
        // Set up test environment
        await this.setupTestEnvironment();
        
        // Run tests
        const result = await this.runTests();
        
        if (result.success) {
            console.log(chalk.green('✅ All tests passed!'));
        } else {
            console.log(chalk.red('❌ Some tests failed.'));
            process.exit(1);
        }
    }
    
    async setupTestEnvironment() {
        // Mock FAP runtime for tests
        const mockRuntime = `
        global.fap = {
            loadComponent: async (name) => {
                const componentPath = path.join(process.cwd(), 'components', name, 'index.js');
                return require(componentPath);
            },
            platform: { type: 'test' },
            capabilities: {}
        };
        
        // Mock DOM
        const { JSDOM } = require('jsdom');
        const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
        global.window = dom.window;
        global.document = dom.window.document;
        global.navigator = dom.window.navigator;
        `;
        
        // Write test setup file
        await require('fs').promises.writeFile(
            path.join(process.cwd(), 'test-setup.js'),
            mockRuntime
        );
    }
    
    async runTests() {
        return new Promise((resolve) => {
            const jest = spawn('jest', [
                '--setupFilesAfterEnv=./test-setup.js',
                '--testMatch=**/components/**/*.test.js',
                this.options.watch ? '--watch' : '',
                this.options.coverage ? '--coverage' : ''
            ].filter(Boolean), {
                stdio: 'inherit'
            });
            
            jest.on('close', (code) => {
                resolve({ success: code === 0 });
            });
        });
    }
}

module.exports = FAPTestRunner;
```

### 6.5 Build System (Day 4-5)
```javascript
// lib/builder.js
const fs = require('fs').promises;
const path = require('path');
const chalk = require('chalk');

class FAPBuilder {
    constructor(options = {}) {
        this.options = {
            output: 'dist',
            minify: true,
            sourceMaps: true,
            ...options
        };
    }
    
    async build() {
        console.log(chalk.yellow('📦 Building FAP application...'));
        
        // Clean output directory
        await this.cleanOutput();
        
        // Copy static files
        await this.copyStaticFiles();
        
        // Process HTML files
        await this.processHTMLFiles();
        
        // Bundle components (if any local components)
        await this.bundleComponents();
        
        // Generate manifest
        await this.generateManifest();
        
        console.log(chalk.green('✅ Build complete!'));
        console.log(chalk.cyan(`📁 Output: ${this.options.output}`));
    }
    
    async cleanOutput() {
        try {
            await fs.rmdir(this.options.output, { recursive: true });
        } catch (e) {
            // Directory doesn't exist, that's OK
        }
        
        await fs.mkdir(this.options.output, { recursive: true });
    }
    
    async copyStaticFiles() {
        // Copy all static files (excluding components and node_modules)
        const files = await this.findFiles('.', {
            ignore: ['node_modules/**', 'components/**', 'dist/**', '.*']
        });
        
        for (const file of files) {
            const outputPath = path.join(this.options.output, file);
            const outputDir = path.dirname(outputPath);
            
            await fs.mkdir(outputDir, { recursive: true });
            await fs.copyFile(file, outputPath);
        }
    }
    
    async processHTMLFiles() {
        // Find all HTML files in output directory
        const htmlFiles = await this.findFiles(this.options.output, {
            pattern: '**/*.html'
        });
        
        for (const htmlFile of htmlFiles) {
            let content = await fs.readFile(htmlFile, 'utf-8');
            
            // Replace FAP runtime references
            content = content.replace(
                /fap:\/\/runtime\//g,
                'https://cdn.fap.dev/runtime/v1/'
            );
            
            // Add offline support
            content = content.replace(
                '</head>',
                `  <link rel="manifest" href="./manifest.json">
  <script>
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('./sw.js');
    }
  </script>
</head>`
            );
            
            await fs.writeFile(htmlFile, content);
        }
    }
    
    async bundleComponents() {
        const componentsDir = path.join('components');
        
        try {
            const components = await fs.readdir(componentsDir);
            
            if (components.length > 0) {
                console.log(chalk.blue('📦 Bundling local components...'));
                
                // Create components bundle
                const bundle = await this.createComponentsBundle(componentsDir);
                
                await fs.writeFile(
                    path.join(this.options.output, 'components.js'),
                    bundle
                );
            }
        } catch (e) {
            // No local components directory
        }
    }
    
    async createComponentsBundle(componentsDir) {
        let bundle = `// FAP Components Bundle
(function() {
  window.fapLocalComponents = {};
`;
        
        const components = await fs.readdir(componentsDir);
        
        for (const componentName of components) {
            const componentDir = path.join(componentsDir, componentName);
            const indexPath = path.join(componentDir, 'index.js');
            const stylePath = path.join(componentDir, 'style.css');
            
            try {
                // Read component JavaScript
                const jsContent = await fs.readFile(indexPath, 'utf-8');
                
                // Read component CSS
                let cssContent = '';
                try {
                    cssContent = await fs.readFile(stylePath, 'utf-8');
                } catch (e) {
                    // No CSS file
                }
                
                // Add component to bundle
                bundle += `
  // Component: ${componentName}
  window.fapLocalComponents['${componentName}'] = (function() {
    // Inject CSS
    if (${JSON.stringify(cssContent)}) {
      const style = document.createElement('style');
      style.textContent = ${JSON.stringify(cssContent)};
      document.head.appendChild(style);
    }
    
    // Component code
    const exports = {};
    const module = { exports: {} };
    ${jsContent}
    
    return module.exports || exports;
  })();
`;
            } catch (e) {
                console.warn(chalk.yellow(`⚠️  Could not bundle component: ${componentName}`));
            }
        }
        
        bundle += `
})();`;
        
        return bundle;
    }
    
    async generateManifest() {
        // Read package.json for app info
        let packageJson = {};
        try {
            const content = await fs.readFile('package.json', 'utf-8');
            packageJson = JSON.parse(content);
        } catch (e) {
            // No package.json
        }
        
        const manifest = {
            name: packageJson.name || 'FAP Application',
            short_name: packageJson.name || 'FAP App',
            description: packageJson.description || 'A FAP application',
            start_url: '.',
            display: 'standalone',
            theme_color: '#007acc',
            background_color: '#ffffff',
            icons: [
                {
                    src: 'icon-192.png',
                    sizes: '192x192',
                    type: 'image/png'
                },
                {
                    src: 'icon-512.png',
                    sizes: '512x512',
                    type: 'image/png'
                }
            ]
        };
        
        await fs.writeFile(
            path.join(this.options.output, 'manifest.json'),
            JSON.stringify(manifest, null, 2)
        );
    }
    
    async findFiles(dir, options = {}) {
        // Simple file finder implementation
        // In real implementation, use glob or similar
        return [];
    }
}

module.exports = FAPBuilder;
```

## Testing Plan
1. **CLI Commands**: Test all CLI commands and options
2. **Dev Server**: Test hot reloading and proxy functionality
3. **Component Generation**: Test generated component quality
4. **Build Process**: Test build output and optimization
5. **Testing Framework**: Test component testing capabilities

## Deliverables
- [ ] FAP CLI tool with all commands
- [ ] Development server with hot reloading
- [ ] Component generator and templates
- [ ] Testing framework integration
- [ ] Build system with optimization
- [ ] Documentation generator

## Next Phase
Once developer tools are complete, create `TRACKING.md` and `DECISIONS.md` for project management.

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"id": "1", "content": "Create 03-PROTOCOL.md - fap:// protocol design", "status": "completed"}, {"id": "2", "content": "Create 04-COMPONENTS.md - Component system design", "status": "completed"}, {"id": "3", "content": "Create 05-PLATFORMS.md - Cross-platform integration", "status": "completed"}, {"id": "4", "content": "Create 06-DEVELOPER.md - Development tools", "status": "completed"}, {"id": "5", "content": "Create TRACKING.md - Progress tracking", "status": "in_progress"}, {"id": "6", "content": "Create DECISIONS.md - Technical decisions log", "status": "pending"}]