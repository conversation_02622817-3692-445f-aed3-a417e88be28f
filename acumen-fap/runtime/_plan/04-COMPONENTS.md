# Phase 4: Component System Design

## Goal
Create a modular component system that enables shared UI components, utilities, and features across all FAP applications.

## Technical Requirements
- **Semantic Versioning**: Components use semver (1.2.3)
- **Lazy Loading**: Components load on-demand
- **Dependency Resolution**: Automatic dependency management
- **Hot Reloading**: Development-time component updates

## Component Architecture

### 4.1 Component Structure (Day 1)
```
~/.fap/runtime/components/
├── ui/
│   ├── button/
│   │   ├── v1.0.0/
│   │   │   ├── index.js       # Component logic
│   │   │   ├── style.css      # Component styles
│   │   │   ├── manifest.json  # Metadata
│   │   │   └── README.md      # Documentation
│   │   ├── v1.1.0/
│   │   └── latest → v1.1.0    # Symlink to latest
│   └── modal/
└── utils/
    ├── crypto/
    └── validation/
```

### 4.2 Component Manifest Format (Day 1)
```json
// manifest.json
{
  "name": "ui/button",
  "version": "1.1.0",
  "description": "Customizable button component",
  "author": "FAP Team",
  "license": "MIT",
  "main": "index.js",
  "style": "style.css",
  "dependencies": {
    "utils/events": "^1.0.0"
  },
  "peerDependencies": {
    "ui/theme": ">=2.0.0"
  },
  "exports": {
    "default": "Button",
    "ButtonGroup": "ButtonGroup"
  },
  "tags": ["ui", "form", "interactive"],
  "compatibility": {
    "web": true,
    "tauri": true,
    "electron": true,
    "pear": true,
    "cli": false
  },
  "size": {
    "js": "2.1kb",
    "css": "1.3kb",
    "gzipped": "1.8kb"
  }
}
```

### 4.3 Component Registry System (Day 1-2)
```javascript
// fap-registry.js
class FAPRegistry {
    constructor() {
        this.components = new Map();
        this.loaded = new Set();
        this.loading = new Map();
        this.registry = null;
    }
    
    async loadRegistry() {
        if (this.registry) return this.registry;
        
        try {
            const response = await fetch('fap://runtime/registry.json');
            this.registry = await response.json();
            return this.registry;
        } catch (error) {
            console.warn('Failed to load component registry:', error);
            this.registry = { components: {} };
            return this.registry;
        }
    }
    
    async register(name, version, manifest) {
        await this.loadRegistry();
        
        if (!this.registry.components[name]) {
            this.registry.components[name] = {};
        }
        
        this.registry.components[name][version] = manifest;
        
        // Update registry file
        await this.saveRegistry();
    }
    
    async resolve(name, versionRange = 'latest') {
        await this.loadRegistry();
        
        const componentVersions = this.registry.components[name];
        if (!componentVersions) {
            throw new Error(`Component "${name}" not found`);
        }
        
        const version = this.resolveVersion(componentVersions, versionRange);
        if (!version) {
            throw new Error(`No compatible version found for "${name}@${versionRange}"`);
        }
        
        return { name, version, manifest: componentVersions[version] };
    }
    
    resolveVersion(versions, range) {
        if (range === 'latest') {
            return this.getLatestVersion(Object.keys(versions));
        }
        
        // Simple semver resolution (can be enhanced)
        for (const version of Object.keys(versions).sort()) {
            if (this.satisfiesRange(version, range)) {
                return version;
            }
        }
        
        return null;
    }
    
    getLatestVersion(versions) {
        return versions.sort((a, b) => this.compareVersions(b, a))[0];
    }
    
    compareVersions(a, b) {
        const aParts = a.split('.').map(Number);
        const bParts = b.split('.').map(Number);
        
        for (let i = 0; i < Math.max(aParts.length, bParts.length); i++) {
            const aPart = aParts[i] || 0;
            const bPart = bParts[i] || 0;
            
            if (aPart !== bPart) {
                return aPart - bPart;
            }
        }
        
        return 0;
    }
    
    satisfiesRange(version, range) {
        // Simple range matching - can be enhanced with proper semver
        if (range.startsWith('^')) {
            const targetVersion = range.slice(1);
            return this.compareVersions(version, targetVersion) >= 0;
        }
        
        return version === range;
    }
}
```

### 4.4 Component Loader (Day 2-3)
```javascript
// fap-component-loader.js
class FAPComponentLoader {
    constructor(registry) {
        this.registry = registry;
        this.cache = new Map();
        this.dependencies = new Map();
    }
    
    async load(name, versionRange = 'latest') {
        const key = `${name}@${versionRange}`;
        
        if (this.cache.has(key)) {
            return this.cache.get(key);
        }
        
        // Resolve component version
        const { name: resolvedName, version, manifest } = await this.registry.resolve(name, versionRange);
        const componentKey = `${resolvedName}@${version}`;
        
        if (this.cache.has(componentKey)) {
            this.cache.set(key, this.cache.get(componentKey));
            return this.cache.get(componentKey);
        }
        
        // Load dependencies first
        await this.loadDependencies(manifest);
        
        // Load the component
        const component = await this.loadComponent(resolvedName, version, manifest);
        
        this.cache.set(componentKey, component);
        this.cache.set(key, component);
        
        return component;
    }
    
    async loadDependencies(manifest) {
        const { dependencies = {}, peerDependencies = {} } = manifest;
        
        const allDeps = { ...dependencies, ...peerDependencies };
        const loadPromises = [];
        
        for (const [depName, depRange] of Object.entries(allDeps)) {
            loadPromises.push(this.load(depName, depRange));
        }
        
        await Promise.all(loadPromises);
    }
    
    async loadComponent(name, version, manifest) {
        const basePath = `fap://runtime/components/${name}/${version}`;
        
        // Load CSS if present
        if (manifest.style) {
            await this.loadStylesheet(`${basePath}/${manifest.style}`);
        }
        
        // Load JavaScript
        const jsPath = `${basePath}/${manifest.main}`;
        const module = await this.loadModule(jsPath);
        
        // Validate exports
        this.validateExports(module, manifest.exports);
        
        return {
            name,
            version,
            manifest,
            module,
            exports: this.extractExports(module, manifest.exports)
        };
    }
    
    async loadStylesheet(url) {
        const response = await fetch(url);
        const css = await response.text();
        
        const style = document.createElement('style');
        style.textContent = css;
        style.setAttribute('data-component', url);
        document.head.appendChild(style);
    }
    
    async loadModule(url) {
        const response = await fetch(url);
        const code = await response.text();
        
        // Create module scope
        const moduleScope = {
            exports: {},
            module: { exports: {} },
            require: (name) => {
                const cached = this.cache.get(name);
                if (cached) {
                    return cached.exports;
                }
                throw new Error(`Module "${name}" not found`);
            },
            fap: window.fap
        };
        
        // Execute module code
        const func = new Function('exports', 'module', 'require', 'fap', code);
        func(moduleScope.exports, moduleScope.module, moduleScope.require, moduleScope.fap);
        
        return moduleScope.module.exports || moduleScope.exports;
    }
    
    validateExports(module, expectedExports) {
        if (!expectedExports) return;
        
        for (const [exportName, exportValue] of Object.entries(expectedExports)) {
            if (!(exportValue in module)) {
                console.warn(`Expected export "${exportValue}" not found in component`);
            }
        }
    }
    
    extractExports(module, exportMap) {
        if (!exportMap) return module;
        
        const exports = {};
        for (const [exportName, moduleProp] of Object.entries(exportMap)) {
            exports[exportName] = module[moduleProp];
        }
        
        return exports;
    }
}
```

### 4.5 Example Component Implementation (Day 3)
```javascript
// components/ui/button/v1.1.0/index.js
(function(exports, module, require, fap) {
    'use strict';
    
    const events = require('utils/events');
    
    class Button {
        constructor(options = {}) {
            this.options = {
                text: 'Click me',
                variant: 'primary',
                size: 'medium',
                disabled: false,
                ...options
            };
            
            this.element = null;
            this.listeners = new Map();
        }
        
        render(container) {
            this.element = document.createElement('button');
            this.element.className = this.getClasses();
            this.element.textContent = this.options.text;
            this.element.disabled = this.options.disabled;
            
            // Add event listeners
            this.element.addEventListener('click', (e) => {
                this.emit('click', e);
            });
            
            if (container) {
                container.appendChild(this.element);
            }
            
            return this.element;
        }
        
        getClasses() {
            const { variant, size, disabled } = this.options;
            let classes = ['fap-button'];
            
            classes.push(`fap-button--${variant}`);
            classes.push(`fap-button--${size}`);
            
            if (disabled) {
                classes.push('fap-button--disabled');
            }
            
            return classes.join(' ');
        }
        
        on(event, callback) {
            if (!this.listeners.has(event)) {
                this.listeners.set(event, new Set());
            }
            this.listeners.get(event).add(callback);
            
            return () => this.off(event, callback);
        }
        
        off(event, callback) {
            const eventListeners = this.listeners.get(event);
            if (eventListeners) {
                eventListeners.delete(callback);
            }
        }
        
        emit(event, ...args) {
            const eventListeners = this.listeners.get(event);
            if (eventListeners) {
                for (const callback of eventListeners) {
                    callback(...args);
                }
            }
        }
        
        setText(text) {
            this.options.text = text;
            if (this.element) {
                this.element.textContent = text;
            }
        }
        
        setDisabled(disabled) {
            this.options.disabled = disabled;
            if (this.element) {
                this.element.disabled = disabled;
                this.element.className = this.getClasses();
            }
        }
        
        destroy() {
            if (this.element && this.element.parentNode) {
                this.element.parentNode.removeChild(this.element);
            }
            this.listeners.clear();
            this.element = null;
        }
    }
    
    // Export the component
    module.exports = { Button };
    
})(exports, module, require, fap);
```

### 4.6 Component CSS (Day 3)
```css
/* components/ui/button/v1.1.0/style.css */
.fap-button {
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-family: inherit;
    font-weight: 500;
    line-height: 1;
    text-decoration: none;
    transition: all 0.2s ease;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
}

.fap-button:focus {
    outline: 2px solid var(--fap-focus-color, #007acc);
    outline-offset: 2px;
}

.fap-button:active {
    transform: translateY(1px);
}

/* Sizes */
.fap-button--small {
    font-size: 12px;
    min-height: 24px;
    padding: 0 8px;
}

.fap-button--medium {
    font-size: 14px;
    min-height: 32px;
    padding: 0 16px;
}

.fap-button--large {
    font-size: 16px;
    min-height: 40px;
    padding: 0 24px;
}

/* Variants */
.fap-button--primary {
    background: var(--fap-primary-color, #007acc);
    color: var(--fap-primary-text-color, white);
}

.fap-button--primary:hover {
    background: var(--fap-primary-hover-color, #005a9e);
}

.fap-button--secondary {
    background: var(--fap-secondary-color, #6c757d);
    color: var(--fap-secondary-text-color, white);
}

.fap-button--outline {
    background: transparent;
    border: 1px solid var(--fap-border-color, #ccc);
    color: var(--fap-text-color, #333);
}

.fap-button--disabled {
    cursor: not-allowed;
    opacity: 0.5;
    transform: none !important;
}
```

### 4.7 Component Usage (Day 4)
```html
<!-- Example app using FAP components -->
<!DOCTYPE html>
<html>
<head>
    <title>FAP App Example</title>
    <script src="fap://runtime/bootstrap.js"></script>
</head>
<body>
    <div id="app"></div>
    
    <script>
        document.addEventListener('fap:runtime:ready', async () => {
            // Load button component
            const { Button } = await fap.loadComponent('ui/button', '^1.0.0');
            
            // Create button instance
            const myButton = new Button({
                text: 'Hello FAP!',
                variant: 'primary',
                size: 'large'
            });
            
            // Add event listener
            myButton.on('click', () => {
                alert('Button clicked!');
            });
            
            // Render to page
            myButton.render(document.getElementById('app'));
        });
    </script>
</body>
</html>
```

## Testing Plan
1. **Registry**: Test component registration and resolution
2. **Loading**: Test dependency resolution and loading order
3. **Versioning**: Test semver compatibility and latest resolution
4. **CSS**: Test style isolation and loading
5. **Hot Reload**: Test development-time component updates

## Deliverables
- [ ] Component registry system
- [ ] Component loader with dependency resolution
- [ ] Example UI components (button, modal, form)
- [ ] Component development tools
- [ ] Component documentation templates
- [ ] Version management system

## Next Phase
Once component system is complete, move to `05-PLATFORMS.md` for cross-platform integration.

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"id": "1", "content": "Create 03-PROTOCOL.md - fap:// protocol design", "status": "completed"}, {"id": "2", "content": "Create 04-COMPONENTS.md - Component system design", "status": "completed"}, {"id": "3", "content": "Create 05-PLATFORMS.md - Cross-platform integration", "status": "in_progress"}, {"id": "4", "content": "Create 06-DEVELOPER.md - Development tools", "status": "pending"}, {"id": "5", "content": "Create TRACKING.md - Progress tracking", "status": "pending"}, {"id": "6", "content": "Create DECISIONS.md - Technical decisions log", "status": "pending"}]