# Phase 2: Core Runtime Architecture

## Goal
Design and implement the core FAP Runtime that provides shared functionality across all application types.

## Technical Requirements
- **Startup Time**: Under 500ms
- **Memory Usage**: Under 50MB baseline
- **API Surface**: Consistent across platforms
- **Component Loading**: Lazy loading with caching

## Architecture Design

### 2.1 Runtime Core Structure
```
~/.fap/runtime/core/
├── fap-core.js         # Main runtime engine
├── fap-loader.js       # Dynamic module loader
├── fap-events.js       # Event system
├── fap-storage.js      # Local storage abstraction
├── fap-network.js      # Network utilities
└── fap-platform.js     # Platform detection/adaptation
```

### 2.2 Module System Design (Day 1-2)
Create dynamic module loader:

```javascript
// fap-loader.js
class FAPLoader {
    constructor() {
        this.cache = new Map();
        this.loading = new Map();
        this.baseURL = 'fap://runtime/';
    }
    
    async load(modulePath) {
        if (this.cache.has(modulePath)) {
            return this.cache.get(modulePath);
        }
        
        if (this.loading.has(modulePath)) {
            return this.loading.get(modulePath);
        }
        
        const promise = this._loadModule(modulePath);
        this.loading.set(modulePath, promise);
        
        try {
            const module = await promise;
            this.cache.set(modulePath, module);
            return module;
        } finally {
            this.loading.delete(modulePath);
        }
    }
    
    async _loadModule(modulePath) {
        const response = await fetch(`${this.baseURL}${modulePath}`);
        const code = await response.text();
        
        // Create isolated module scope
        const moduleScope = new Function('exports', 'require', 'module', code);
        const exports = {};
        const module = { exports };
        
        moduleScope(exports, this.require.bind(this), module);
        return module.exports;
    }
}

window.fap = window.fap || {};
window.fap.loader = new FAPLoader();
```

### 2.3 Core Runtime API (Day 2-3)
Define consistent API surface:

```javascript
// fap-core.js
window.fap = {
    version: '1.0.0',
    
    // Module system
    loader: null, // Set by fap-loader.js
    
    // Core utilities
    storage: null, // Set by fap-storage.js
    events: null,  // Set by fap-events.js
    network: null, // Set by fap-network.js
    
    // Platform detection
    platform: {
        type: 'unknown', // 'web', 'tauri', 'electron', 'pear', 'cli'
        os: 'unknown',   // 'windows', 'macos', 'linux'
        capabilities: []
    },
    
    // Component system
    components: new Map(),
    
    // Lifecycle
    ready: false,
    readyPromise: null,
    
    // API methods
    async init() {
        if (this.readyPromise) return this.readyPromise;
        
        this.readyPromise = this._initialize();
        return this.readyPromise;
    },
    
    async _initialize() {
        // Detect platform
        this.platform = await this._detectPlatform();
        
        // Load core modules
        this.storage = await this.loader.load('core/fap-storage.js');
        this.events = await this.loader.load('core/fap-events.js');
        this.network = await this.loader.load('core/fap-network.js');
        
        // Initialize platform-specific features
        await this._initializePlatform();
        
        this.ready = true;
        this.events.emit('fap:ready');
    },
    
    async loadComponent(name, version = 'latest') {
        const key = `${name}@${version}`;
        
        if (this.components.has(key)) {
            return this.components.get(key);
        }
        
        const component = await this.loader.load(`components/${name}/${version}/index.js`);
        this.components.set(key, component);
        
        return component;
    }
};
```

### 2.4 Storage Abstraction (Day 3)
Unified storage across platforms:

```javascript
// fap-storage.js
class FAPStorage {
    constructor() {
        this.adapter = this._selectAdapter();
    }
    
    _selectAdapter() {
        if (typeof window.__TAURI__ !== 'undefined') {
            return new TauriStorageAdapter();
        } else if (typeof window.electronAPI !== 'undefined') {
            return new ElectronStorageAdapter();
        } else if ('localStorage' in window) {
            return new WebStorageAdapter();
        } else {
            return new MemoryStorageAdapter();
        }
    }
    
    async get(key) {
        return this.adapter.get(key);
    }
    
    async set(key, value) {
        return this.adapter.set(key, value);
    }
    
    async delete(key) {
        return this.adapter.delete(key);
    }
    
    async clear() {
        return this.adapter.clear();
    }
}

// Platform-specific adapters
class WebStorageAdapter {
    async get(key) {
        const value = localStorage.getItem(key);
        return value ? JSON.parse(value) : null;
    }
    
    async set(key, value) {
        localStorage.setItem(key, JSON.stringify(value));
    }
    
    // ... etc
}

module.exports = new FAPStorage();
```

### 2.5 Event System (Day 4)
Lightweight pub/sub system:

```javascript
// fap-events.js
class FAPEvents {
    constructor() {
        this.listeners = new Map();
    }
    
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, new Set());
        }
        this.listeners.get(event).add(callback);
        
        // Return unsubscribe function
        return () => this.off(event, callback);
    }
    
    off(event, callback) {
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            eventListeners.delete(callback);
        }
    }
    
    emit(event, ...args) {
        const eventListeners = this.listeners.get(event);
        if (eventListeners) {
            for (const callback of eventListeners) {
                try {
                    callback(...args);
                } catch (error) {
                    console.error(`Error in event listener for "${event}":`, error);
                }
            }
        }
    }
    
    once(event, callback) {
        const unsubscribe = this.on(event, (...args) => {
            unsubscribe();
            callback(...args);
        });
        return unsubscribe;
    }
}

module.exports = new FAPEvents();
```

### 2.6 Runtime Bootstrap Script (Day 4-5)
Single script that initializes FAP Runtime:

```javascript
// runtime-bootstrap.js (embedded in apps)
(function() {
    // Check if FAP Runtime is available locally
    const FAP_LOCAL_URL = 'http://localhost:8080/runtime/core/fap-core.js';
    const FAP_CDN_URL = 'https://cdn.fap.dev/runtime/v1/fap-core.js';
    
    async function loadRuntime() {
        try {
            // Try local runtime first
            await loadScript(FAP_LOCAL_URL);
            console.log('FAP Runtime loaded from local server');
        } catch (error) {
            // Fallback to CDN
            await loadScript(FAP_CDN_URL);
            console.log('FAP Runtime loaded from CDN');
        }
        
        // Initialize runtime
        await window.fap.init();
        
        // Notify app that runtime is ready
        window.dispatchEvent(new CustomEvent('fap:runtime:ready'));
    }
    
    function loadScript(url) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = url;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
    
    // Start loading when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadRuntime);
    } else {
        loadRuntime();
    }
})();
```

## Testing Plan
1. **Module Loading**: Test dynamic module loading
2. **Storage**: Test storage across all platforms
3. **Events**: Test event system performance
4. **Bootstrap**: Test runtime initialization
5. **Fallback**: Test CDN fallback when local unavailable

## Deliverables
- [ ] Core runtime modules (6 files)
- [ ] Dynamic module loader
- [ ] Cross-platform storage abstraction
- [ ] Event system
- [ ] Runtime bootstrap script
- [ ] Comprehensive test suite

## Next Phase
Once core runtime is complete, move to `03-PROTOCOL.md` for fap:// protocol implementation.