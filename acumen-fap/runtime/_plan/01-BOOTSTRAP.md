# Phase 1: Bootstrap Tauri App

## Goal
Create minimal Tauri installer that establishes FAP Runtime infrastructure on user's machine.

## Technical Requirements
- **Size Target**: Under 5MB
- **Platforms**: Windows, macOS, Linux
- **Installation**: Single executable, no admin required
- **Runtime Directory**: `~/.fap/` (cross-platform user directory)

## Implementation Tasks

### 1.1 Project Setup (Day 1)
```bash
# Create Tauri project
cargo create-tauri-app fap-bootstrap --template vanilla
cd fap-bootstrap

# Configure minimal build
# Target: --release --bundle --features=
```

**Files to create:**
- `src-tauri/Cargo.toml` - Minimal dependencies
- `src-tauri/tauri.conf.json` - App configuration
- `src/index.html` - Bootstrap UI

### 1.2 Directory Structure (Day 1-2)
Create `~/.fap/` directory structure:

```
~/.fap/
├── runtime/
│   ├── core/           # Essential framework files
│   ├── modules/        # Feature modules
│   ├── components/     # UI components
│   └── cache/          # Download cache
├── apps/               # Installed FAP apps
├── config/
│   ├── settings.json   # User preferences
│   └── registry.json   # Component registry
└── logs/               # System logs
```

**Rust implementation:**
```rust
use dirs::home_dir;
use std::fs;

fn create_fap_directories() -> Result<PathBuf, Box<dyn Error>> {
    let home = home_dir().ok_or("Cannot find home directory")?;
    let fap_root = home.join(".fap");
    
    // Create directory structure
    fs::create_dir_all(fap_root.join("runtime/core"))?;
    fs::create_dir_all(fap_root.join("runtime/modules"))?;
    // ... etc
    
    Ok(fap_root)
}
```

### 1.3 Local HTTP Server (Day 2-3)
Embedded server for serving runtime files:

```rust
use warp::Filter;

#[tokio::main]
async fn start_local_server() {
    let routes = warp::path("runtime")
        .and(warp::fs::dir("~/.fap/runtime/"));
        
    warp::serve(routes)
        .run(([127, 0, 0, 1], 8080))
        .await;
}
```

**Features:**
- Serve files from `~/.fap/runtime/`
- CORS headers for cross-origin requests
- Gzip compression
- File watching for development

### 1.4 Initial Download System (Day 3-4)
Download core runtime files:

```rust
async fn download_core_runtime() -> Result<(), Box<dyn Error>> {
    let core_files = [
        ("fap-core.js", "https://runtime.fap.dev/core/fap-core.js"),
        ("fap-ui.css", "https://runtime.fap.dev/core/fap-ui.css"),
        ("fap-utils.js", "https://runtime.fap.dev/core/fap-utils.js"),
    ];
    
    for (filename, url) in core_files {
        download_file(url, &get_fap_path().join("runtime/core").join(filename)).await?;
    }
    
    Ok(())
}
```

### 1.5 Bootstrap UI (Day 4-5)
Simple HTML interface for installation:

```html
<!-- src/index.html -->
<!DOCTYPE html>
<html>
<head>
    <title>FAP Runtime Bootstrap</title>
    <style>
        body { font-family: system-ui; margin: 40px; }
        .progress { width: 100%; height: 20px; }
        .status { margin: 10px 0; }
    </style>
</head>
<body>
    <h1>FAP Runtime Bootstrap</h1>
    <div class="status" id="status">Initializing...</div>
    <progress class="progress" id="progress" value="0" max="100"></progress>
    
    <script>
        const { invoke } = window.__TAURI__.tauri;
        
        async function bootstrap() {
            document.getElementById('status').textContent = 'Creating directories...';
            await invoke('create_directories');
            
            document.getElementById('status').textContent = 'Downloading runtime...';
            await invoke('download_runtime');
            
            document.getElementById('status').textContent = 'Starting server...';
            await invoke('start_server');
            
            document.getElementById('status').textContent = 'Bootstrap complete!';
        }
        
        bootstrap();
    </script>
</body>
</html>
```

## Testing Plan
1. **Size check**: Verify bundle under 5MB
2. **Cross-platform**: Test on Windows/macOS/Linux  
3. **Directory creation**: Verify `~/.fap/` structure
4. **Download**: Test runtime file downloads
5. **Server**: Verify localhost:8080 serves files

## Deliverables
- [ ] Tauri bootstrap executable (<5MB)
- [ ] Cross-platform directory creation
- [ ] Local HTTP server (port 8080)
- [ ] Core runtime file downloads
- [ ] Bootstrap UI with progress tracking

## Next Phase
Once bootstrap is complete, move to `02-RUNTIME.md` for core runtime architecture.