# LandMax Real Estate Business

**Private Implementation Built on FAP Platform**

LandMax represents a collection of real estate businesses that serve as a customer implementation of the FAP (Freedom Application Platform). This demonstrates how FAP can be used to build complete business solutions.

## Structure

```
landmax/
├── apps-web/           # LandMax web applications
│   ├── property-portal/ # Property management system
│   ├── client-portal/  # Client interface and portal
│   └── agent-tools/    # Real estate agent tools
├── apps-desktop/       # LandMax desktop applications
│   └── property-manager/ # Desktop property management
├── apps-p2p/           # LandMax P2P applications (future)
└── data-private/       # LandMax business data (PRIVATE)
    ├── properties/     # Property listings and data
    ├── clients/        # Client information and records
    ├── transactions/   # Transaction records and history
    ├── legal/          # Legal documents and contracts
    └── processes/      # Internal business processes
```

## Business Model

LandMax serves as both:
- **Customer of Acumen** - Acumen Desktop Software Canada Inc. provides software development services
- **Implementation Example** - Shows how FAP platform can be used for real-world business applications
- **Separate Legal Entity** - Independent business with its own operations and data

## Privacy and Security

### Private Data
All LandMax business data is **strictly private** and includes:
- **Customer Information** - Client contacts, preferences, transaction history
- **Property Data** - Listings, valuations, market analysis
- **Business Processes** - Internal workflows, procedures, training materials
- **Legal Documents** - Contracts, agreements, compliance records

### Access Control
- **LandMax Team Only** - Access restricted to authorized LandMax personnel
- **Separate from FAP** - No LandMax data included in FAP open source components
- **Audit Trail** - All access to sensitive data logged and monitored

## Technology Stack

### Built on FAP Platform
LandMax applications leverage FAP components:
- **FAP Core** - Theme system and base utilities
- **FAP UI Components** - Chat, tooltips, toast notifications
- **FAP Architecture** - Semantic HTML and vanilla JavaScript approach

### Shared Resources
- **Assets** - Uses shared assets from `/assets/` directory
- **Documentation** - Non-sensitive docs in `/docs/landmax/`
- **Data System** - Integrates with shared `/data/` database system

### Custom Extensions
- **Real Estate Specific** - Property management, client tracking, transaction processing
- **Business Logic** - Custom workflows for real estate operations
- **Integration** - MLS systems, mapping services, financial tools

## Usage

### Development
```bash
# Work on LandMax applications
cd landmax/apps-web/property-portal/
pnpm dev
```

### Data Management
```bash
# Access private business data (authorized personnel only)
cd landmax/data-private/
# Follow data access protocols
```

## Compliance

### Canadian Privacy Laws
- **PIPEDA Compliance** - Personal Information Protection and Electronic Documents Act
- **Data Retention** - Appropriate retention and disposal policies
- **Consent Management** - Client consent tracking and management

### Real Estate Regulations
- **Provincial Licensing** - Compliance with provincial real estate regulations
- **Transaction Records** - Proper documentation and record keeping
- **Client Trust** - Trust account management and reporting

## Integration Points

### With FAP Platform
- **Component Library** - Uses FAP UI components
- **Development Tools** - Uses FAP development and build tools
- **Architecture** - Follows FAP semantic HTML principles

### With Acumen Corporate
- **Service Agreement** - Software development services from Acumen
- **Support** - Technical support and maintenance
- **Legal** - Contracts and agreements managed through Acumen

### With Shared Systems
- **Database** - Uses shared TerminusDB for appropriate data
- **Assets** - References shared assets and branding
- **Documentation** - Contributes to shared knowledge base (non-sensitive)
