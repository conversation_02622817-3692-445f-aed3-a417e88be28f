use lancedb::database::Database;
use serde::{Deserialize, Serialize};
use std::path::Path;

#[derive(Debug, Serialize, Deserialize)]
struct Item {
    id: u32,
    vector: Vec<f32>,
    text: String,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("LanceDB Example");
    println!("===============\n");
    
    // Set up the database path
    let db_path = "data/lancedb";
    
    // Create the data directory if it doesn't exist
    if !Path::new("data").exists() {
        std::fs::create_dir("data")?;
    }
    
    // Connect to the database (creates it if it doesn't exist)
    println!("Connecting to database at: {}", db_path);
    let db = Database::connect(db_path).await?;
    
    // Create a table with a schema
    println!("\nCreating table 'items'...");
    let schema = serde_json::json!([
        {"name": "id", "type": "uint32"},
        {"name": "vector", "type": "vector(128)"},
        {"name": "text", "type": "string"}
    ]);
    
    // Create or get the table
    let table = db.create_table("items", schema).await?;
    
    // Prepare some sample data
    println!("\nInserting sample data...");
    let items = vec![
        Item {
            id: 1,
            vector: vec![0.1; 128],
            text: "First item".to_string(),
        },
        Item {
            id: 2,
            vector: vec![0.2; 128],
            text: "Second item".to_string(),
        },
        Item {
            id: 3,
            vector: vec![0.3; 128],
            text: "Third item".to_string(),
        },
    ];
    
    // Convert items to JSON and insert into the table
    let json_data = serde_json::to_value(&items)?;
    table.add(&json_data).await?;
    
    // Perform a search
    println!("\nPerforming a search...");
    let query_vector = vec![0.15; 128];
    let results = table.search(&query_vector)
        .limit(2)
        .execute()
        .await?;
    
    // Display search results
    println!("\nSearch results:");
    println!("---------------");
    for (i, result) in results.as_array().unwrap().iter().enumerate() {
        let id = result["id"].as_u64().unwrap();
        let text = result["text"].as_str().unwrap();
        let distance = result["_distance"].as_f64().unwrap();
        println!("{}: ID={}, Text='{}', Distance={:.4}", i + 1, id, text, distance);
    }
    
    // List all tables in the database
    println!("\nTables in the database:");
    println!("----------------------");
    let tables = db.list_tables().await?;
    for table in tables {
        println!("- {}", table);
    }
    
    println!("\nExample completed successfully!");
    Ok(())
}
