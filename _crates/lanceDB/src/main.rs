use lancedb::{connect, Connection};
use serde::{Deserialize, Serialize};
use std::path::Path;

#[derive(Debug, Serialize, Deserialize)]
struct Item {
    id: u32,
    vector: Vec<f32>,
    text: String,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("LanceDB Example");
    println!("===============\n");
    
    // Set up the database path
    let db_path = "data/lancedb";
    
    // Create the data directory if it doesn't exist
    if !Path::new("data").exists() {
        std::fs::create_dir("data")?;
    }
    
    // Connect to the database
    println!("Connecting to database at: {}", db_path);
    let db = connect(db_path).execute().await?;
    
    // List all tables
    println!("\nTables in the database:");
    println!("----------------------");
    let tables = db.list_tables().await?;
    for table in tables {
        println!("- {}", table);
    }
    
    println!("\nExample completed!");
    Ok(())
}
