use lancedb::connect;
use std::path::Path;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("LanceDB Example");
    println!("===============\n");
    
    // Set up the database path
    let db_path = "data/lancedb";
    
    // Create the data directory if it doesn't exist
    if !Path::new("data").exists() {
        std::fs::create_dir("data")?;
    }
    
    // Connect to the database
    println!("Connecting to database at: {}", db_path);
    let db = connect(db_path).execute().await?;
    
    // Print the type of db to help with debugging
    println!("\nConnection type: {}", std::any::type_name::<_>(&db));
    
    // Try to list tables using the available methods
    println!("\nAttempting to list tables...");
    
    // Check if the connection has a tables() method
    if let Ok(tables) = db.tables().await {
        println!("\nTables in the database:");
        println!("----------------------");
        for table in tables {
            println!("- {}", table);
        }
    } else {
        println!("\nNo tables() method found on Connection");
    }
    
    // Try to get the version
    println!("\nLanceDB version: {:?}", lancedb::version());
    
    println!("\nExample completed!");
    Ok(())
}
