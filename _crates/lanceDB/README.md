# LanceDB Integration

## Goal
Integrate LanceDB (v0.21.2) as a vector database for semantic search capabilities within the FAP monorepo, specifically to complement TerminusDB's graph database functionality.

## Progress

### Completed Tasks
- [x] Created a new Rust crate in `_crates/lanceDB`
- [x] Added lancedb v0.21.2 as a dependency
- [x] Set up basic project structure with Cargo.toml
- [x] Created a minimal working example that initializes a LanceDB connection
- [x] Successfully built and ran a basic binary

### Current Status
- Successfully connected to LanceDB using `lancedb::connect()`
- Basic connection and table listing functionality implemented
- Currently investigating the correct API usage for database operations

### Challenges Faced
1. **API Confusion**: The lancedb crate's API differs from the documentation. The `Database` struct doesn't have a `connect` method; instead, we need to use the `connect` function from the root module.

2. **ConnectBuilder Pattern**: The `connect()` function returns a `ConnectBuilder` which requires calling `.execute().await?` to establish a connection.

3. **Missing Methods**: Some expected methods like `list_tables()` are not available on the `Connection` struct, requiring alternative approaches.

4. **Limited Documentation**: The Rust documentation for lancedb is sparse, making it challenging to determine the correct usage patterns.

## Next Steps
1. Investigate the available methods on the `Connection` type at runtime
2. Determine the correct way to create tables and perform vector searches
3. Implement basic CRUD operations for vector data
4. Integrate with the existing FAP architecture

## Example Code
Current working example in `src/main.rs` demonstrates basic connection and type inspection.

## Dependencies
- lancedb = "0.21.2"
- tokio = { version = "1.0", features = ["full"] }
- serde = { version = "1.0", features = ["derive"] }
- serde_json = "1.0"
