# Agent Creation Guidelines

## Design Principles

### Single Responsibility
- **One domain focus**: Database, frontend, backend, etc.
- **Clear boundaries**: Avoid overlapping capabilities
- **Specific expertise**: Deep knowledge in narrow area

### Proactive Triggers
- **Keywords**: Include domain-specific terms in description
- **Action phrases**: "MUST BE USED for", "PROA<PERSON><PERSON><PERSON><PERSON> handles"
- **Context clues**: Technology names, file types, patterns

## Configuration Template

```yaml
---
name: domain-role
description: |
  [Expert role] specializing in [specific area]. MUST BE USED for 
  [specific trigger scenarios]. PROACTIVELY handles [task types] 
  involving [relevant keywords/technologies].
tools: Read, Write, Bash(relevant-commands*), mcp__relevant__*
---

Expert in [domain] with deep knowledge of:
- [Core competency 1]
- [Core competency 2] 
- [Core competency 3]

Development focus:
- ES6+ functional programming patterns
- Immutable data structures
- Modern async/await syntax
- [Domain-specific practices]

Provide practical examples using functional programming principles.
Always consider performance and maintainability.
```

## Tool Selection Guidelines

### Essential Tools
- **Read/Write**: Always include for file operations
- **Bash**: Limit to relevant commands (npm*, git*, test*)
- **MCP**: Only domain-specific integrations

### Tool Restrictions
- **Security**: Limit powerful tools to trusted agents
- **Focus**: Fewer tools = better performance
- **Specificity**: Use wildcards for command families

## System Prompt Best Practices

### Structure
1. **Role definition**: Clear expertise statement
2. **Core competencies**: 3-5 key skills
3. **Development principles**: Functional programming focus
4. **Output guidance**: Examples, formatting, approach

### Language Patterns
- **Imperative tone**: "Analyze", "Design", "Implement"
- **Specific examples**: Include code patterns
- **Constraints**: Mention limitations or requirements
- **Context awareness**: Reference project needs

## Quality Checklist

### Before Creation
- [ ] Unique domain not covered by existing agents
- [ ] Clear, specific name following `domain-role` pattern
- [ ] Proactive description with trigger keywords
- [ ] Minimal, relevant tool selection
- [ ] Comprehensive system prompt with examples

### Testing New Agents
- [ ] Responds to explicit invocation
- [ ] Triggers automatically for relevant tasks
- [ ] Provides domain-appropriate responses
- [ ] No conflicts with existing agents
- [ ] Follows functional programming principles

### Documentation Requirements
- [ ] Added to agent index under correct domain
- [ ] Description explains when to use
- [ ] System prompt includes technical focus areas
- [ ] Tools justified for agent's role
- [ ] Version controlled with descriptive commit