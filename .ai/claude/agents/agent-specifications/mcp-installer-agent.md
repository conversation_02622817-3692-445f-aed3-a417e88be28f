# MCP Installer Sub-Agent Detailed Specification

## Agent Overview

**Name**: `mcp-installer`  
**Category**: Infrastructure & Development Tools  
**Purpose**: Specialized MCP server research, installation, and documentation specialist  
**Status**: Fully designed and tested (2025-07-28)  
**Complexity**: Advanced (handles complex troubleshooting and documentation)

## Core Design Philosophy

The MCP Installer sub-agent embodies the principle of **isolated expertise**. Rather than cluttering main development sessions with MCP server research and troubleshooting, this agent operates in its own context to handle the complete lifecycle of MCP server management.

### Key Design Principles
1. **Context Isolation** - MCP research doesn't pollute main coding session
2. **Systematic Methodology** - Repeatable process for installation and troubleshooting
3. **Knowledge Building** - Creates persistent documentation for future use
4. **Real Problem Solving** - Goes beyond simple command automation

## Technical Capabilities

### Enhanced Tool Access
```yaml
tools: 
  - Read, Write, Edit, MultiEdit  # File operations
  - WebFetch                      # Documentation research
  - Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp:*)  # MCP CLI
  - mcp__playwright__*            # Deep web research
```

### Research Superpowers
The agent can perform sophisticated research when primary sources fail:

**Fallback Research Chain**:
1. **WebFetch** to official repositories (https://github.com/modelcontextprotocol/servers)
2. **WebFetch** to specific server documentation
3. **Playwright browser automation** for complex documentation sites
4. **Pattern recognition** from previous successful installations

### Installation Methodologies
The agent has learned from real troubleshooting experiences:

#### Standard Patterns
```bash
# Try standard approach first
claude mcp add --scope user <name> npx @modelcontextprotocol/server-<name>
```

#### Alternative Approaches (Learned from Real Failures)
```bash
# UV/UVX pattern (discovered with git server)
claude mcp add --scope user <name> uvx mcp-server-<name>

# Path arguments (discovered with filesystem server)  
claude mcp add --scope user <name> npx @server <path>

# Complex authentication (discovered with Ref server)
claude mcp add-json --scope user <name> '{"command": "npx", "args": [...]}'
```

## Real-World Learning Examples

### Case Study 1: Git Server Success
**Problem**: `npx @modelcontextprotocol/server-git` failed to connect  
**Solution**: WebFetch research revealed `uvx mcp-server-git` is correct method  
**Learning**: Package managers matter - some servers use uv ecosystem  
**Pattern**: Always check official docs for preferred installation method

### Case Study 2: Filesystem Security
**Problem**: Server failed without path argument  
**Solution**: Research revealed security model requires explicit directory access  
**Learning**: Security-first design patterns in file operation servers  
**Pattern**: Path arguments common for file system access tools

### Case Study 3: Ref Authentication
**Problem**: Multiple authentication failures with different approaches  
**Solution**: Documented failed attempts and working configuration for future  
**Learning**: Complex authentication requires JSON configuration method  
**Pattern**: HTTP vs stdio transport differences in authentication

## Specification File System

The agent creates detailed documentation for each server in:
`acumen-fap/ai/claude/mcp_servers/mcp-specifications/<server-name>.md`

### Template Structure
Each specification includes:
- **Status tracking** (✅ working, ❌ failed, ⚠️ partial)
- **Exact working commands** 
- **Failed attempts with reasons**
- **Troubleshooting guides**
- **Sub-agent learning notes**

### Knowledge Persistence
Unlike traditional sub-agents that lose context, the specification system creates:
- **Permanent knowledge base** of working configurations
- **Troubleshooting trails** for complex issues  
- **Pattern documentation** for future installations
- **Error prevention** by documenting what doesn't work

## Advanced Problem-Solving Capabilities

### Multi-Approach Testing
```bash
# Systematic approach testing:
1. Standard: npx @modelcontextprotocol/server-name
2. Alternative: uvx mcp-server-name  
3. With path: command /path/to/directory
4. JSON config: complex authentication scenarios
5. Environment variables: stdio with credentials
```

### Health Monitoring
```bash
# Real-time validation:
claude mcp list                    # Connection status
claude mcp get <name>             # Detailed configuration  
claude mcp remove <name>          # Clean up failed attempts
```

### Documentation Generation
For each successful installation:
1. **Research phase** documented
2. **Installation attempts** tracked (both failed and successful)
3. **Configuration details** preserved
4. **Usage examples** provided
5. **Troubleshooting guide** created

## Use Case Examples

### Scenario 1: Database Integration
```bash
"mcp-installer: Research and install PostgreSQL MCP server for database operations"
```
**Agent Process**:
1. WebFetch to find PostgreSQL MCP servers
2. Research official vs community options
3. Test installation with multiple approaches
4. Create specification file with working configuration
5. Report back: "PostgreSQL server installed at mcp__postgres__* tools available"

### Scenario 2: Troubleshooting Failed Server
```bash
"mcp-installer: Fix the GitHub MCP server that's showing connection errors"  
```
**Agent Process**:
1. Read current configuration with `claude mcp get github`
2. Research GitHub MCP authentication requirements
3. Test different authentication methods
4. Update specification file with solution
5. Report working configuration or documented limitations

### Scenario 3: Development Stack Setup
```bash
"mcp-installer: Set up complete development stack with git, database, testing, and documentation servers"
```
**Agent Process**:
1. Research each server type independently
2. Install in order of complexity (simple first)
3. Test each before moving to next
4. Create specification files for all
5. Provide final working development environment

## Integration with Main Sessions

### Context Preservation
The sub-agent operates independently, allowing main sessions to:
- **Continue coding** while MCP research happens in background
- **Receive clean summaries** rather than research details
- **Get working configurations** ready to use immediately
- **Access specification files** for future reference

### Handoff Process
1. **User requests** MCP capability in main session
2. **Sub-agent summoned** for research and installation
3. **Agent works independently** with full tool access
4. **Results reported back** with working configuration
5. **Main session continues** with new MCP tools available

## Benefits Over Manual Process

### Efficiency
- **Parallel research** while main work continues
- **Systematic approach** prevents random trial-and-error
- **Pattern recognition** from previous successes/failures
- **Documentation generation** happens automatically

### Quality
- **Comprehensive research** using multiple sources
- **Real troubleshooting** beyond simple command execution
- **Knowledge persistence** via specification files
- **Continuous learning** from each installation

### User Experience
- **Clean main session** without research clutter
- **Expert-level results** from specialized agent
- **Persistent knowledge** for future use
- **Systematic documentation** of entire MCP ecosystem

## Future Enhancements

### Learning System
- **Pattern database** of successful configurations
- **Failure analysis** to improve troubleshooting
- **Version tracking** for MCP server updates
- **Compatibility matrix** across different systems

### Integration Opportunities
- **Automatic updates** when new MCP servers released
- **Health monitoring** of existing installations
- **Security analysis** of MCP server permissions
- **Performance optimization** of MCP configurations

## Conclusion

The MCP Installer sub-agent represents a sophisticated approach to specialized task delegation. Rather than simple command automation, it provides genuine expertise, systematic problem-solving, and knowledge building that benefits future sessions.

This agent demonstrates the power of sub-agents for:
- **Complex technical tasks** requiring research and troubleshooting
- **Knowledge persistence** beyond individual sessions
- **Context isolation** for specialized work
- **Expert-level capabilities** in specific domains

The specification file system and systematic approach make this agent genuinely useful rather than just a convenience wrapper around CLI commands.