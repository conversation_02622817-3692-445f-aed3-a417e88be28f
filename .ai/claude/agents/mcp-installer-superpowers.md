# MCP Installer Sub-Agent: Troubleshooting Superpowers
*Designed from real troubleshooting experience - 2025-07-28*

## Discovered Issues & Solutions

### Problem 1: Ref MCP Tools Not Working
**Symptom**: `mcp__Ref__ref_search_documentation` returns "No valid session ID provided"
**Status**: Shows "✓ Connected" but tools fail
**Root Cause**: Session/authentication issue with HTTP-based MCP server
**Impact**: Can't use Ref for documentation lookup during troubleshooting

### Problem 2: Git MCP Server Connection Failure
**Symptom**: `git: npx @modelcontextprotocol/server-git - ✗ Failed to connect`
**Solution Found**: Use `uvx mcp-server-git` instead of `npx @modelcontextprotocol/server-git`
**Discovery Method**: WebFetch to official GitHub repository documentation

## Sub-Agent Troubleshooting Superpowers

### Enhanced Tool Access
```yaml
tools: 
  - Read, Write, Edit, MultiEdit
  - WebFetch  # Critical for documentation lookup
  - Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp:*)  # MCP CLI commands
  - mcp__playwright__*  # Browser automation for deep research
```

### Troubleshooting Methodology

#### 1. Documentation Research Powers
**When Ref MCP fails** (as it did):
```bash
# Fallback research chain:
1. WebFetch to https://github.com/modelcontextprotocol/servers
2. WebFetch to specific server docs (e.g., /tree/main/src/git)
3. mcp__playwright__browser_navigate to complex documentation sites
4. Search for installation patterns, common issues
```

#### 2. Installation Debugging Powers
```bash
# Test multiple installation approaches:
# Try standard pattern first
claude mcp add --scope user <name> npx @modelcontextprotocol/server-<name>

# If fails, research alternatives:
# - uvx instead of npx (git server solution)
# - Different package names
# - Required path arguments
# - Environment variables needed
```

#### 3. Real-time Health Monitoring
```bash
# After each attempt:
claude mcp list  # Check connection status
claude mcp get <name>  # Get detailed configuration
claude mcp remove <name>  # Clean up failed attempts
```

#### 4. Error Pattern Recognition
**Common Patterns Discovered**:
- **npx vs uvx**: Some servers use uv/uvx instead of npm/npx
- **Path requirements**: filesystem, git servers often need path arguments
- **Package naming**: Not always `@modelcontextprotocol/server-<name>`
- **HTTP vs stdio**: Different transport types have different failure modes

### Enhanced Sub-Agent Capabilities

#### WebFetch Integration
```markdown
When server installation fails:
1. WebFetch to official repository
2. Find specific server documentation
3. Look for installation section
4. Check for troubleshooting guides
5. Search for GitHub issues if needed
```

#### Playwright Powers
```markdown
For complex documentation sites:
1. Navigate to detailed setup pages
2. Screenshot configuration examples
3. Extract installation commands from complex docs
4. Handle JavaScript-heavy documentation sites
```

#### Pattern Learning
```markdown
Build knowledge base of working patterns:
- "git server needs uvx not npx"
- "filesystem needs path argument"
- "Ref tools may show connected but fail due to auth"
- "Always test with 'claude mcp list' after adding"
```

## Sub-Agent Invocation Examples

### Research & Install
```bash
"mcp-installer: Research and install PostgreSQL database server"
# Agent will:
# 1. WebFetch to find postgres MCP documentation
# 2. Try installation with standard pattern
# 3. If fails, research alternative commands
# 4. Test with claude mcp list
# 5. Report working configuration
```

### Troubleshoot Existing
```bash
"mcp-installer: Fix the Ref server tools that aren't working"
# Agent will:
# 1. Get current server details
# 2. Research authentication/session requirements
# 3. Try reinstallation or reconfiguration
# 4. Report solution or limitations
```

### Bulk Setup
```bash
"mcp-installer: Set up development stack with git, filesystem, database, and GitHub servers"
# Agent will:
# 1. Research each server individually
# 2. Install in order of complexity (simple first)
# 3. Test each before moving to next
# 4. Provide final working configuration
```

## Success Metrics

### Before Enhancement
- ❌ Ref MCP tools not working
- ❌ Git server failed to connect
- ❌ Limited troubleshooting capabilities
- ❌ No systematic approach to failures

### After Enhancement  
- ✅ Multiple research fallback methods
- ✅ Pattern recognition from documentation
- ✅ Systematic retry with alternatives
- ✅ Real troubleshooting methodology
- ✅ Git server now working with uvx command

## Implementation Notes

The sub-agent needs:
1. **WebFetch access** for documentation research
2. **Playwright access** for complex sites
3. **Full MCP CLI access** for testing
4. **Pattern recognition** from previous attempts
5. **Systematic methodology** not just trial-and-error

This makes the sub-agent genuinely useful rather than just automating the same commands a human would try.