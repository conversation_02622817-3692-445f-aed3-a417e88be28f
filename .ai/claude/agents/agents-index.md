# Claude Code Agent Index
*Self-managing agent registry for Acumen PNPM Monorepo development*

---
version: 1.0.0
last_updated: 2025-01-28
total_agents: 25
active_agents: 4
status: developing
maintainer: agent-manager
next_review: 2025-02-04
---

## Current Priority Agents (Acumen FAP Infrastructure)

<details>
<summary>Foundation (4 agents) - Primary Focus</summary>

| Agent | Status | Description | Last Updated |
|-------|--------|-------------|--------------|
| `monorepo-architect` | ✅ Active | PNPM workspaces, nx/lerna patterns, dependency management | 2025-01-28 |
| `docs-manager` | ✅ Active | Technical documentation, API specs, README standards | 2025-01-28 |
| `structure-organizer` | ⏸️ Planned | File naming, directory hierarchy, code organization | - |
| `research-analyst` | ⏸️ Planned | Technology evaluation, architecture decisions, feasibility studies | - |

</details>

<details>
<summary>Database (3 agents) - Core Development</summary>

| Agent | Status | Description | Last Updated |
|-------|--------|-------------|--------------|
| `schema-designer` | ⏸️ Planned | Database modeling, migrations, relationships | - |
| `query-optimizer` | ⏸️ Planned | SQL performance, indexing strategies | - |
| `data-validator` | ⏸️ Planned | Input validation, sanitization, type checking | - |

</details>

<details>
<summary>Frontend (4 agents) - Core Development</summary>

| Agent | Status | Description | Last Updated |
|-------|--------|-------------|--------------|
| `html-architect` | ⏸️ Planned | Semantic markup, accessibility, web standards | - |
| `css-specialist` | ⏸️ Planned | Modern CSS, responsive design, animations | - |
| `js-functional` | ✅ Active | ES6+ patterns, immutable state, functional composition | 2025-01-28 |
| `ui-components` | ⏸️ Planned | Reusable patterns, design systems, component architecture | - |

</details>

<details>
<summary>Backend (3 agents) - Core Development</summary>

| Agent | Status | Description | Last Updated |
|-------|--------|-------------|--------------|
| `api-architect` | ⏸️ Planned | REST/GraphQL design, endpoint planning, middleware | - |
| `node-specialist` | ⏸️ Planned | Node.js patterns, stream handling, performance | - |
| `microservices` | ⏸️ Planned | Service decomposition, inter-service communication | - |

</details>

<details>
<summary>P2P Technologies (4 agents) - Specialized</summary>

| Agent | Status | Description | Last Updated |
|-------|--------|-------------|--------------|
| `pear-specialist` | ✅ Active | Pear runtime, P2P app development, Holepunch ecosystem | 2025-01-28 |
| `bare-expert` | ⏸️ Planned | Bare.js runtime, low-level operations, system integration | - |
| `hypercore-stack` | ⏸️ Planned | Hypercore, Hyperdrive, Hyperswarm, DHT | - |
| `network-protocols` | ⏸️ Planned | P2P networking, NAT traversal, connection management | - |

</details>

<details>
<summary>Electron (4 agents) - Desktop Development</summary>

| Agent | Status | Description | Last Updated |
|-------|--------|-------------|--------------|
| `main-process` | ⏸️ Planned | App lifecycle, native APIs, system integration | - |
| `renderer-process` | ⏸️ Planned | UI rendering, browser contexts, sandboxing | - |
| `ipc-specialist` | ⏸️ Planned | Inter-process communication, contextBridge patterns | - |
| `security-auditor` | ⏸️ Planned | Electron security, CSP, process isolation | - |

</details>

<details>
<summary>DevOps & Build (4 agents) - Infrastructure</summary>

| Agent | Status | Description | Last Updated |
|-------|--------|-------------|--------------|
| `package-builder` | ⏸️ Planned | electron-builder, distribution, code signing | - |
| `ci-cd-manager` | ⏸️ Planned | GitHub Actions, automated testing, deployment | - |
| `dependency-auditor` | ⏸️ Planned | npm security, version management, vulnerability scanning | - |
| `performance-profiler` | ⏸️ Planned | Memory analysis, CPU profiling, bundle optimization | - |

</details>

<details>
<summary>Code Quality (4 agents) - Standards</summary>

| Agent | Status | Description | Last Updated |
|-------|--------|-------------|--------------|
| `functional-reviewer` | ⏸️ Planned | Pure functions, immutability, composition patterns | - |
| `test-generator` | ⏸️ Planned | Unit, integration, e2e test creation | - |
| `code-formatter` | ⏸️ Planned | ESLint, Prettier, consistent styling | - |
| `security-scanner` | ⏸️ Planned | OWASP compliance, vulnerability detection | - |

</details>

<details>
<summary>Meta-Management (3 agents) - System Management</summary>

| Agent | Status | Description | Last Updated |
|-------|--------|-------------|--------------|
| `agent-manager` | 🔄 In Progress | Creates, updates, removes agents from this index | 2025-01-28 |
| `settings-manager` | ✅ Active | Modifies Claude Code configuration files | 2025-01-28 |
| `markdown-specialist` | 🔄 In Progress | Enhanced markdown, documentation formatting, living docs | 2025-01-28 |

</details>

---

## Agent Configuration Standards

### File Structure
- **Location**: `.claude/agents/` (project-level)
- **Naming**: `domain-role.md` (e.g., `database-schema-designer.md`)
- **Format**: YAML frontmatter + Markdown system prompt

### Status Indicators
- ✅ **Active**: Currently implemented and working
- 🔄 **In Progress**: Being created or updated
- ⏸️ **Planned**: Designed but not yet implemented
- ❌ **Deprecated**: Retired or replaced
- 🧪 **Experimental**: Testing phase

### Required Fields
```yaml
---
name: domain-role
description: |
  Specific expertise area. MUST BE USED for [trigger scenarios].
  PROACTIVELY handles [task types] in [domain context].
tools: Read, Write, Bash(relevant*), mcp__*
status: active|planned|deprecated
created: YYYY-MM-DD
last_updated: YYYY-MM-DD
version: x.y.z
---
```

---

## Usage Patterns

### Explicit Invocation
```bash
"monorepo-architect: Design workspace structure for this project"
"pear-specialist: Create P2P file sharing component"
"markdown-specialist: Enhance this documentation with interactive elements"
```

### Automatic Delegation
- Keywords in descriptions trigger agent selection
- Use "MUST BE USED" and "PROACTIVELY" for automatic activation
- Multiple agents can collaborate on complex tasks

### Management Commands
```bash
/agents                              # Open agent management interface
"agent-manager: Add new agent for [domain]"
"agent-manager: Update agent status in index"
"settings-manager: Update Claude Code configuration"
```

---

## Change Log

| Date | Change | Agent | Version |
|------|--------|--------|---------|
| 2025-01-28 | Enhanced with interactive elements, status tracking | `markdown-specialist` | 1.0.0 |
| 2025-01-28 | Initial index creation | `agent-manager` | 1.0.0 |

---

*Last auto-updated: 2025-01-28 by agent-manager*