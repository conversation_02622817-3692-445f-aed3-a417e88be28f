# Agent Management Instructions

## Core Management Tasks

### Adding New Agents
1. **Identify need**: Domain gap or new technology requirement
2. **Check index**: Verify agent doesn't already exist
3. **Create config**: Use agent creation guidelines
4. **Update index**: Add to appropriate domain section
5. **Test invocation**: Verify agent responds correctly

### Updating Existing Agents
1. **Review performance**: Check if agent meets requirements
2. **Update config**: Modify tools, description, or system prompt
3. **Version control**: Note changes in git commits
4. **Update index**: Revise descriptions if needed

### Removing Agents
1. **Assess usage**: Confirm agent is no longer needed
2. **Archive config**: Move to `.claude/agents/archive/`
3. **Update index**: Remove from active registry
4. **Document reason**: Note removal rationale

## Index Maintenance

### Structure Rules
- **Domain grouping**: Organize by primary responsibility
- **Consistent naming**: Use `domain-role` pattern
- **Clear descriptions**: Include trigger keywords and use cases
- **Priority ordering**: Most important agents first in each section

### Regular Reviews
- **Weekly**: Check agent performance and usage
- **Monthly**: Review domain organization and gaps
- **Quarterly**: Assess overall agent ecosystem health

## Agent Lifecycle Management

### Creation Triggers
- New technology adoption (e.g., adding React, Vue)
- Performance bottlenecks in existing agents
- Team expansion requiring specialized roles
- Project phase changes (development → deployment)

### Retirement Criteria
- Technology deprecation
- Merged functionality with other agents
- Consistently poor performance
- No usage for 30+ days

## Configuration Standards

### File Organization
```
.claude/agents/
├── domain-role.md          # Active agents
├── archive/               # Retired agents
└── experimental/          # Testing new agents
```

### Version Control
- Commit agent changes with descriptive messages
- Tag major agent system updates
- Maintain changelog for significant modifications

## Quality Assurance

### Before Adding Agents
- Test with sample tasks
- Verify tool permissions work correctly
- Ensure no conflicts with existing agents
- Validate description triggers automatic selection

### Performance Monitoring
- Track agent invocation frequency
- Monitor task completion success rates
- Gather user feedback on agent effectiveness
- Identify redundant or overlapping capabilities