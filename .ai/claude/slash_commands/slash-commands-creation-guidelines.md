# Slash Commands Creation Guidelines

## Design Principles

### Single Workflow Focus
- **One clear process**: Specific task or workflow
- **Agent orchestration**: Chain multiple agents for complex tasks
- **Reusable patterns**: Common workflows your team uses repeatedly

### Command Structure
- **Namespace organization**: Group related commands (foundation/, development/, p2p/)
- **Clear naming**: `namespace-action.md` (e.g., `review-security.md`)
- **Parameter support**: Use `$ARGUMENTS` for dynamic content

## Command Template

```markdown
# Command: project:namespace:action

Brief description of the workflow and when to use it.

## Purpose
What problem this command solves and expected outcomes.

## Agents Involved
- agent-name: Specific role in this workflow
- another-agent: Additional expertise needed

## Workflow Steps
1. Step description with agent assignment
2. Next step with different agent
3. Final consolidation step

## Prompt
[Main prompt content here]

Use agent-name to analyze $ARGUMENTS for [specific criteria].
Then have another-agent to [follow-up action].
Finally, provide a summary of findings and recommendations.

## Usage Examples
```bash
/project:namespace:action file.js
/project:namespace:action "specific criteria"
```

## Expected Output
Description of what the user should expect as results.
```

## Workflow Design Patterns

### Sequential Agent Chain
```markdown
1. Use security-auditor to scan for vulnerabilities
2. Have functional-reviewer optimize any fixes  
3. Ask test-generator to create security tests
4. Provide consolidated security report
```

### Parallel Analysis
```markdown
Simultaneously:
- performance-profiler: Analyze memory and CPU usage
- functional-reviewer: Check ES6+ patterns
- security-auditor: Scan for vulnerabilities

Consolidate all findings into actionable recommendations.
```

### Agent + Tool Integration
```markdown
Use pear-specialist to analyze P2P architecture.
Run performance tests with available tools.
Generate optimization recommendations with code examples.
```

## File Organization Standards

### Namespace Structure
```
.claude/commands/
├── foundation/          # Project setup, structure, docs
├── development/         # Code review, testing, quality
├── p2p/                # Pear, Bare, P2P-specific workflows
├── electron/           # Desktop app workflows
├── build/              # CI/CD, packaging, deployment
└── meta/               # Agent and system management
```

### Naming Conventions
- **Action-based**: `review-security.md`, `generate-tests.md`
- **Lowercase with hyphens**: No spaces or special characters
- **Descriptive**: Clear purpose from filename

## Quality Checklist

### Before Creation
- [ ] Unique workflow not covered by existing commands
- [ ] Clear namespace and naming following conventions
- [ ] Appropriate agents identified for each step
- [ ] `$ARGUMENTS` used for dynamic content where needed
- [ ] Expected output clearly defined

### Testing New Commands
- [ ] Command executes without errors
- [ ] Agents respond appropriately to workflow steps
- [ ] Output matches expected results
- [ ] Parameters work correctly with `$ARGUMENTS`
- [ ] Documentation is clear and complete

### Documentation Requirements
- [ ] Added to commands index under correct namespace
- [ ] Clear description of purpose and usage
- [ ] Agent involvement documented
- [ ] Usage examples provided
- [ ] Version controlled with descriptive commit