# Slash Commands Management Instructions

## Core Management Tasks

### Adding New Commands
1. **Identify workflow need**: Repetitive process or agent chain
2. **Check index**: Verify command doesn't already exist
3. **Choose namespace**: Select appropriate directory (foundation/, development/, p2p/, etc.)
4. **Create command file**: Use creation guidelines template
5. **Update index**: Add to appropriate namespace section
6. **Test execution**: Verify command works with intended agents

### Updating Existing Commands
1. **Review performance**: Check if command achieves desired workflow
2. **Update prompt**: Modify agent instructions or workflow steps
3. **Test changes**: Ensure agents still respond appropriately
4. **Update index**: Revise descriptions and status if needed
5. **Version control**: Commit changes with descriptive message

### Removing Commands
1. **Assess usage**: Confirm command is no longer needed
2. **Archive file**: Move to `.claude/commands/archive/`
3. **Update index**: Remove from active registry
4. **Document reason**: Note removal rationale in changelog

## Index Maintenance

### Structure Rules
- **Namespace organization**: Group by workflow type (foundation, development, p2p, meta)
- **Consistent naming**: Use `namespace-action.md` pattern
- **Clear descriptions**: Include agent dependencies and use cases
- **Status tracking**: Maintain current status indicators

### Regular Reviews
- **Weekly**: Check command usage and effectiveness
- **Monthly**: Review namespace organization and identify gaps
- **Quarterly**: Analyze agent interaction patterns and optimize workflows

## Command Lifecycle Management

### Creation Triggers
- **Repetitive workflows**: Team does same agent sequence repeatedly
- **Complex orchestration**: Multi-agent tasks need standardization
- **New technology**: Pear updates, new tools require specialized workflows
- **Quality gates**: Standard review processes for code/docs

### Retirement Criteria
- **Workflow obsolescence**: Process no longer needed
- **Agent changes**: Required agents deprecated or merged
- **Poor performance**: Consistently fails to achieve goals
- **No usage**: Command unused for 30+ days

## File Organization Standards

### Directory Structure
```
.claude/commands/
├── foundation/           # Active foundation commands
├── development/          # Active development commands  
├── p2p/                 # Active P2P commands
├── electron/            # Active Electron commands
├── build/               # Active build/CI commands
├── meta/                # Active meta commands
├── archive/             # Retired commands
└── experimental/        # Testing new commands
```

### Version Control
- **Descriptive commits**: Clear explanation of command changes
- **Tag releases**: Major command system updates
- **Track dependencies**: Note when commands depend on specific agents

## Quality Assurance

### Before Adding Commands
- **Test with target agents**: Verify agents respond to workflow steps
- **Validate parameters**: Check `$ARGUMENTS` substitution works
- **Ensure no conflicts**: No duplicate or overlapping commands
- **Document clearly**: Purpose, agents, usage examples included

### Performance Monitoring
- **Track usage frequency**: Which commands are most/least used
- **Monitor success rates**: Do commands achieve intended outcomes
- **Agent compatibility**: Track when agent updates break commands
- **User feedback**: Gather team input on command effectiveness

## Agent Integration Patterns

### Sequential Workflows
Commands that chain agents in order:
```markdown
1. agent-a: Initial analysis
2. agent-b: Processing step  
3. agent-c: Final validation
```

### Parallel Analysis
Commands that use multiple agents simultaneously:
```markdown
Analyze $ARGUMENTS using:
- security-auditor: Check vulnerabilities
- performance-profiler: Measure efficiency
- functional-reviewer: Validate patterns
```

### Conditional Workflows  
Commands that branch based on results:
```markdown
Use pear-specialist to analyze architecture.
If P2P patterns found, engage network-protocols agent.
Otherwise, use standard api-architect review.
```