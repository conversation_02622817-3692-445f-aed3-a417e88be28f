# Slash Commands Index
*Reusable prompt workflows for Acumen PNPM Monorepo development*

---
version: 1.0.0
last_updated: 2025-01-28
total_commands: 12
active_commands: 0
status: planning
maintainer: commands-manager
location: .claude/commands/
---

## Project Commands (Team Shared)
*Stored in `.claude/commands/` - Available as `/project:command`*

<details>
<summary>Foundation Commands - Project Setup</summary>

| Command | Status | Description | Agents Used |
|---------|--------|-------------|-------------|
| `/project:init:monorepo` | ⏸️ Planned | Initialize PNPM workspace structure | monorepo-architect |
| `/project:docs:readme` | ⏸️ Planned | Generate project README with standards | docs-manager, markdown-specialist |
| `/project:structure:analyze` | ⏸️ Planned | Analyze and optimize file organization | structure-organizer |

</details>

<details>
<summary>Development Commands - Code Workflows</summary>

| Command | Status | Description | Agents Used |
|---------|--------|-------------|-------------|
| `/project:review:security` | ⏸️ Planned | Security audit with functional review | security-auditor, functional-reviewer |
| `/project:review:performance` | ⏸️ Planned | Performance analysis and optimization | performance-profiler, functional-reviewer |
| `/project:test:generate` | ⏸️ Planned | Create comprehensive test suite | test-generator, functional-reviewer |

</details>

<details>
<summary>P2P Commands - Specialized Workflows</summary>

| Command | Status | Description | Agents Used |
|---------|--------|-------------|-------------|
| `/project:p2p:app` | ⏸️ Planned | Create new Pear P2P application | pear-specialist, structure-organizer |
| `/project:p2p:optimize` | ⏸️ Planned | Optimize P2P networking performance | pear-specialist, performance-profiler |

</details>

<details>
<summary>Meta Commands - System Management</summary>

| Command | Status | Description | Agents Used |
|---------|--------|-------------|-------------|
| `/project:agent:create` | ⏸️ Planned | Interactive agent creation workflow | agent-manager |
| `/project:docs:enhance` | ⏸️ Planned | Add interactive elements to documentation | markdown-specialist |
| `/project:commands:review` | ⏸️ Planned | Analyze and optimize slash commands | commands-manager |

</details>

---

## Command Structure Standards

### File Organization
```
.claude/commands/
├── foundation/
│   ├── init-monorepo.md
│   └── docs-readme.md
├── development/
│   ├── review-security.md
│   └── test-generate.md
├── p2p/
│   └── app.md
└── meta/
    └── agent-create.md
```

### Command Template
```markdown
# Command: project:namespace:name

Brief description of what this command does and when to use it.

## Agents Involved
- agent-name: Specific role in workflow
- another-agent: Additional expertise

## Prompt
[Actual prompt content with $ARGUMENTS placeholder]

## Usage Examples
```bash
/project:namespace:name argument1 argument2
```
```

### Status Indicators
- ✅ **Active**: Ready to use
- 🔄 **In Progress**: Being created/tested
- ⏸️ **Planned**: Designed but not implemented
- ❌ **Deprecated**: Retired
- 🧪 **Experimental**: Testing phase

---

## Usage Patterns

### Multi-Agent Workflows
Commands can orchestrate multiple agents:
```markdown
Use security-auditor to scan for vulnerabilities.
Then have functional-reviewer optimize the fixes.
Finally, ask test-generator to create security tests.
```

### Parameterized Commands
Use `$ARGUMENTS` for dynamic content:
```markdown
Analyze the $ARGUMENTS file for performance issues.
```

### Namespace Organization
- **foundation/**: Project setup and structure
- **development/**: Code review and quality
- **p2p/**: Pear and P2P-specific workflows  
- **meta/**: System and agent management

---

## Change Log

| Date | Change | Command | Version |
|------|--------|---------|---------|
| 2025-01-28 | Initial commands index created | - | 1.0.0 |

---

*Last updated: 2025-01-28 by commands-manager*