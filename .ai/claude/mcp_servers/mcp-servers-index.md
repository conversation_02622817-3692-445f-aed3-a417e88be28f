# MCP Servers Index
*Curated MCP servers for Acumen PNPM Monorepo development*

---
version: 1.1.0
last_updated: 2025-07-28
total_servers: 12
active_servers: 5
status: testing
maintainer: mcp-manager
location: .mcp.json
---

## Production Ready (Official/Major Companies)

<details>
<summary>Development Core (4 servers) - Essential</summary>

| Server | Provider | Status | Description | Stars/Quality |
|--------|----------|--------|-------------|---------------|
| `playwright` | Microsoft | ✅ Active | Browser automation, E2E testing | 1.1k+ ⭐ Official |
| `github` | GitHub | ⏸️ Planned | Repository management, CI/CD, code analysis | Official |
| `filesystem` | Anthropic | ✅ Active | Secure file operations with access controls | Official Reference |
| `git` | Anthropic | ✅ Active | Repository operations, commits, branches | Official Reference |

</details>

<details>
<summary>Documentation & Search (2 servers) - Productivity</summary>

| Server | Provider | Status | Description | Stars/Quality |
|--------|----------|--------|-------------|---------------|
| `ref` | Ref Tools | ✅ Active | Documentation search, API references | Well-maintained, API-based |
| `fetch` | Anthropic | ✅ Active | Web content fetching, markdown conversion | Official Reference |

</details>

<details>
<summary>Data & Infrastructure (3 servers) - Stack Support</summary>

| Server | Provider | Status | Description | Stars/Quality |
|--------|----------|--------|-------------|---------------|
| `postgres` | Anthropic | ⏸️ Planned | Database queries, schema inspection | Official Reference |
| `docker` | Community | ⏸️ Planned | Container management, images, networks | Active maintenance |
| `memory` | Anthropic | ⏸️ Planned | Persistent knowledge graph memory | Official Reference |

</details>

<details>
<summary>Optional Integrations (3 servers) - Consider Later</summary>

| Server | Provider | Status | Description | Stars/Quality |
|--------|----------|--------|-------------|---------------|
| `slack` | Community | ❓ Evaluate | Team communication integration | Needs evaluation |
| `reddit` | Community | ❓ Evaluate | Reddit API access | Needs quality check |
| `twitter` | Community | ❓ Evaluate | X/Twitter integration | Multiple options exist |

</details>

---

## Configuration Standards

### Project-Level Setup (Team Shared)
- **Location**: `.mcp.json` in project root
- **Version Control**: Commit to repository
- **Scope**: Shared team configurations

### Quality Criteria
- ✅ **Official/Major Company**: Anthropic, Microsoft, GitHub
- ✅ **High GitHub Stars**: 500+ stars or official backing
- ✅ **Active Maintenance**: Updated within 6 months
- ✅ **Stack Relevance**: Electron, Node.js, P2P, databases
- ❌ **Avoid**: Unmaintained, low-quality, bloated servers

### Template Configuration
```json
{
  "mcpServers": {
    "server-name": {
      "command": "npx",
      "args": ["-y", "package-name"],
      "env": {
        "API_KEY": "<required-token>"
      }
    }
  }
}
```

---

## Integration Patterns

### Agent Invocation
```bash
"security-auditor: Use playwright server to test auth flow"
"functional-reviewer: Check ref documentation for ES6 patterns"
```

### Slash Command Integration
```bash
/project:test:e2e    # Uses playwright + github servers
/project:docs:search # Uses ref + fetch servers
```

### MCP Manager Commands
```bash
"mcp-manager: Add playwright server to project config"
"mcp-manager: Evaluate new server quality"
```

---

## Quick Setup Guide

### 1. Install MCP Manager
```bash
claude mcp add mcp-manager npx mcp-servers-manager
```

### 2. Add Core Servers
```bash
# Essential development stack
claude mcp add playwright npx @playwright/mcp@latest
claude mcp add github npx @modelcontextprotocol/server-github
claude mcp add filesystem npx @modelcontextprotocol/server-filesystem
```

### 3. Configure Environment
- Set required API tokens (GitHub, Ref)
- Configure access permissions
- Test server connections

---

## Status Indicators
- ✅ **Active**: Configured and working
- 🔄 **Installing**: Being added to config
- ⏸️ **Planned**: Approved for future setup
- ❓ **Evaluate**: Needs quality assessment
- ❌ **Rejected**: Fails quality criteria

---

## Change Log

| Date | Change | Server | Version |
|------|--------|--------|---------|
| 2025-07-28 | Updated with actual working status, 5 servers active | playwright, filesystem, git, ref, fetch | 1.1.0 |
| 2025-01-28 | Initial MCP servers index created | - | 1.0.0 |

---

*Last updated: 2025-07-28 by claude-master*