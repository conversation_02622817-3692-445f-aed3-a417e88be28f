# MCP Servers Management Instructions

## Core Management Tasks

### Adding New Servers
1. **Quality check**: Verify GitHub stars, maintenance, official backing
2. **Stack relevance**: Ensure fits Electron/Node.js/P2P development needs
3. **Test locally**: Install and validate functionality
4. **Update .mcp.json**: Add to project configuration
5. **Update index**: Add to appropriate category with status

### Server Quality Criteria
- **Official servers**: Anthropic, Microsoft, GitHub, major companies
- **GitHub stars**: 500+ or official backing required
- **Maintenance**: Updated within 6 months
- **Documentation**: Clear setup and usage instructions
- **Stack fit**: Relevant to team's technology stack

### Configuration Management
- **Project-level**: Use `.mcp.json` for team sharing
- **Environment variables**: Store API keys securely
- **Version control**: Commit configurations to repository
- **Testing**: Validate server connections before deployment

## Server Lifecycle

### Evaluation Process
1. Check official server lists first
2. Verify GitHub repository quality metrics
3. Test installation and basic functionality
4. Assess security and permission requirements

### Removal Criteria
- Unmaintained for 6+ months
- Security vulnerabilities
- Poor performance impact
- No longer relevant to stack

## Integration Patterns

### Agent Integration
Agents can reference MCP servers in their tools list:
```yaml
tools: Read, Write, mcp__playwright__*, mcp__github__*
```

### Slash Command Usage
Commands can orchestrate multiple MCP servers:
```markdown
Use playwright server to navigate to the app.
Then use github server to analyze the repository structure.
```

## Quality Assurance

### Before Adding
- [ ] Meets quality criteria (stars, maintenance, official)
- [ ] Relevant to development stack
- [ ] Security review completed
- [ ] Installation tested successfully
- [ ] Documentation updated

### Regular Reviews
- **Monthly**: Check server health and updates
- **Quarterly**: Reassess stack relevance and usage
- **Security updates**: Monitor for vulnerabilities