# Simple MCP Server Setup Guide
*Updated with REAL process that works - 2025-07-28*

## What Are MCP Servers?
MCP servers are external tools that extend <PERSON>'s capabilities - like connecting to GitHub, automating browsers, or accessing databases.

## CORRECT Process (CLI-Based)

### Prerequisites
- Claude CLI location: `/Users/<USER>/.claude/local/node_modules/.bin/claude`
- Add to PATH or use full path

### Working Global Setup ✅

**Current Status** (verified working - 2025-07-28):
```bash
claude mcp list
# filesystem: ✓ Connected  
# git: ✓ Connected (uvx method)
# sqlite: ✓ Connected (JSON config) 
# playwright: ✓ Connected
# Ref: ✗ Failed (auth issues)
```

### Add Servers (Global/User Scope)

**Correct Command Pattern**:
```bash
# Global scope (available everywhere)
claude mcp add --scope user <name> npx @modelcontextprotocol/server-<name> [path]

# Examples that work:
claude mcp add --scope user filesystem npx @modelcontextprotocol/server-filesystem /path/to/directory
claude mcp add --scope user git uvx mcp-server-git
claude mcp add-json --scope user sqlite '{"command": "uvx", "args": ["mcp-server-sqlite", "--db-path", "/path/to/db.db"]}'
claude mcp add --scope user playwright npx @playwright/mcp@latest
```

### Command Reference

```bash
# List all servers with health check
claude mcp list

# Add server (user/global scope)  
claude mcp add --scope user <name> <command> [args...]

# Remove server
claude mcp remove <name>

# Get help
claude mcp --help
claude mcp add --help
```

## Useful MCP Servers

### Development Essentials
- **github** - Repository management, issues, PRs
- **filesystem** - Secure file operations  
- **git** - Repository operations, commits, branches

### Browser & Testing
- **playwright** - Browser automation, E2E testing ✅ (already installed)

### Documentation & Search
- **ref** - Documentation search, API references ✅ (already installed)
- **fetch** - Web content fetching, markdown conversion

### Database & Infrastructure
- **postgres** - Database queries, schema inspection
- **docker** - Container management

## Simple Usage

Once installed, MCP tools appear as `mcp__servername__toolname`:
- `mcp__github__search_repositories`
- `mcp__playwright__browser_navigate`
- `mcp__ref__ref_search_documentation`

## Configuration File Locations

Your current MCP permissions are in:
- `.claude/settings.local.json` (permissions)
- Global MCP config managed by Claude Code CLI

## Troubleshooting

1. **Check permissions**: Look in `.claude/settings.local.json`
2. **Restart Claude Code** after adding new servers
3. **Use `claude mcp list`** to verify installations

---

*Simple is better than complex. Start with the basics, add more as needed.*