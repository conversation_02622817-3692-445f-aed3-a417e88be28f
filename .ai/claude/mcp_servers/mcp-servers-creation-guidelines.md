# MCP Servers Creation Guidelines

## Evaluation Standards

### Quality Thresholds
- **Official servers**: Always preferred (Anthropic, Microsoft, GitHub)
- **GitHub metrics**: 500+ stars OR official company backing
- **Maintenance**: Active commits within 6 months
- **Documentation**: Clear installation and configuration docs

### Security Assessment
- **Permissions**: Review tool access requirements
- **API keys**: Secure credential management
- **Network access**: Understand external connections
- **Code review**: Check for security vulnerabilities

## Stack Relevance Criteria

### High Priority
- **Development tools**: Git, GitHub, filesystem operations
- **Browser automation**: Playwright for Electron testing
- **Documentation**: Ref for API/library references
- **Databases**: PostgreSQL, Redis for data operations

### Medium Priority
- **Infrastructure**: Docker, CI/CD integration
- **Communication**: Slack, team coordination tools
- **Memory/Storage**: Persistent data and context

### Avoid
- **Unmaintained**: No updates >6 months
- **Low quality**: <100 stars, poor documentation
- **Redundant**: Duplicate functionality
- **Bloated**: Excessive features/complexity

## Configuration Template

```json
{
  "mcpServers": {
    "server-name": {
      "command": "npx",
      "args": ["-y", "package-name@latest"],
      "env": {
        "API_KEY": "<required-if-needed>"
      }
    }
  }
}
```

## Integration Patterns

### Agent Tool Access
```yaml
tools: Read, Write, mcp__server-name__*
```

### Command Orchestration
```markdown
Use server-name to perform specific action.
Then use another-server for follow-up processing.
```

## Quality Checklist

### Before Adding
- [ ] Meets quality thresholds (stars/official)
- [ ] Stack relevant and needed
- [ ] Installation tested successfully
- [ ] Security reviewed
- [ ] No existing alternative

### Documentation
- [ ] Added to index with proper categorization
- [ ] Configuration example provided
- [ ] Integration patterns documented
- [ ] Status and quality metrics noted