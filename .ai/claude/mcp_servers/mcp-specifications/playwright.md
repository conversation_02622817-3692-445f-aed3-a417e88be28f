# Playwright MCP Server Specification

**Docs**: https://github.com/Microsoft/playwright  
**Status**:  Connected and working  
**Installation**: `npx @playwright/mcp@latest` (user scope)

## Overview
Microsoft's official Playwright MCP server providing browser automation, E2E testing, and web interaction capabilities. Essential for testing web applications and automating browser tasks.

## Working Installation
```bash
claude mcp add --scope user playwright npx @playwright/mcp@latest
```

**Key Feature**: No additional arguments or configuration needed

## Available Tools (mcp__playwright__*)
- **Browser control**: Navigate, take screenshots, get page snapshots
- **Element interaction**: Click, type, hover, drag and drop
- **Testing capabilities**: Wait for elements, handle dialogs
- **Network monitoring**: Track requests and responses
- **Tab management**: Create, switch, close browser tabs

## Installation Pattern

###  Working: Standard NPX
```bash
# Global installation, no path required
claude mcp add --scope user playwright npx @playwright/mcp@latest
```

## Browser Capabilities
- **Navigation**: Full page navigation and history control
- **Screenshots**: Capture page or element screenshots
- **Automation**: Complete browser automation for testing
- **Debugging**: Console message capture and network monitoring

## Use Cases
- **E2E testing**: Automated testing of web applications
- **Web scraping**: Complex site navigation and data extraction
- **Browser automation**: Automated form filling, clicking, typing
- **Visual testing**: Screenshot comparison and visual regression testing

## Configuration Notes
- **Microsoft official**: High quality, well-maintained
- **No setup required**: Works immediately after installation
- **Latest version**: Uses `@latest` tag for current features
- **Cross-browser**: Supports Chrome, Firefox, Safari, Edge

## Integration Benefits
- **Mature ecosystem**: Part of established testing framework
- **Reliable connections**: Consistently shows  Connected status
- **Rich tool set**: Comprehensive browser automation capabilities
- **Active development**: Regular updates from Microsoft team

## Sub-Agent Learning
This server demonstrates:
- Official company servers often have simpler installation
- No path arguments needed for browser automation tools
- @latest tag usage for staying current with features
- Reliable connection patterns for major provider tools