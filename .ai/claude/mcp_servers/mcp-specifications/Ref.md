# Ref MCP Server Specification

**Docs**: https://ref.tools/mcp  
**Status**: ✅ Connected, ❌ Tools failing ("No valid session ID provided")  
**Current Config**: Local scope, HTTP transport

## Overview

Ref is an agentic search tool designed to find exactly the right context for coding agents while minimizing token usage and avoiding context rot. It provides intelligent documentation search and web content fetching specifically optimized for LLM workflows.

## Key Features

- **Smart search filtering**: Never returns repeated results in same session
- **Context optimization**: Returns most relevant 5k tokens, drops irrelevant sections
- **Session-aware**: Tracks search trajectory to minimize context usage
- **Cost-effective**: Reduces unnecessary token consumption

## Available Tools

### 1. `ref_search_documentation`

**Purpose**: Powerful search for technical documentation, facts, and code snippets
**Parameters**:

- `query` (required): Full sentence or question to search for

### 2. `ref_read_url`

**Purpose**: Fetches content from URL and converts to markdown
**Parameters**:

- `url` (required): The URL of webpage to read

## Installation Options

### Option 1: HTTP Transport (Recommended)

```bash
claude mcp add --transport http Ref https://api.ref.tools/mcp?apiKey=YOUR_API_KEY
```

````json
{
  "Ref": {
    "command": "npx",
    "args": [
      "mcp-remote@0.1.0-0",
      "https://api.ref.tools/mcp",
      "--header",
      "x-ref-api-key:ref-aae813addce496eb4935"
    ]
  }
}

### Option 2: stdio (Legacy)
```json
{
  "Ref": {
    "command": "npx",
    "args": ["ref-tools-mcp@latest"],
    "env": {
      "REF_API_KEY": "YOUR_API_KEY"
    }
  }
}
````

## Current Issue & Solution

**Problem**: Tools show "No valid session ID provided" error
**Current Config**: `https://api.ref.tools/mcp?apiKey=ref-6f593a687119593eeb29`
**Likely Fix**: Need to sign up for valid API key at https://ref.tools/signup

## Usage Examples

```bash
# Search for documentation
ref_search_documentation("How to write an MCP client")

# Read specific URL
ref_read_url("https://docs.example.com/api")
```

## Integration Notes

- Works with OpenAI deep research (tools renamed to `search()` and `fetch()`)
- Optimized for Claude Code and similar LLM coding agents
- Session tracking reduces redundant searches
- Smart content filtering reduces context bloat
