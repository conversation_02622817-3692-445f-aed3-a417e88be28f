# SQLite MCP Server Specification

**Docs**: https://github.com/modelcontextprotocol/servers-archived/tree/HEAD/src/sqlite#readme  
**Status**: ✅ Connected and working  
**Installation**: `uvx mcp-server-sqlite --db-path <path>` (user scope)

## Overview
SQLite MCP server provides database interaction tools for SQLite databases through the Model Context Protocol. Enables SQL queries, table creation, and business insights generation directly from Claude Code.

## Working Installation
```bash
claude mcp add-json --scope user sqlite '{"command": "uvx", "args": ["mcp-server-sqlite", "--db-path", "/path/to/database.db"]}'
```

**Key Requirement**: Database path argument required for database location

## Available Tools (mcp__sqlite__*)
- **read_query**: Execute SELECT queries to read data
- **write_query**: Execute INSERT, UPDATE, DELETE operations  
- **create_table**: Create new database tables
- **Business insights**: Generates continuous "memo://insights" resource

## Installation Patterns

### ✅ Working: JSON Configuration with uvx
```bash
# Global installation with database path
claude mcp add-json --scope user sqlite '{"command": "uvx", "args": ["mcp-server-sqlite", "--db-path", "/full/path/to/database.db"]}'
```

### ❌ Failed: Direct CLI with arguments
```bash
# This doesn't work - CLI doesn't parse --db-path
claude mcp add --scope user sqlite uvx mcp-server-sqlite --db-path ~/test.db
```

## Current Configuration
- **Database**: `/Volumes/Data/Software/2025/Acumen_Desktop/monorepo_july_2025/test.db`
- **Transport**: stdio via uvx
- **Status**: ✓ Connected
- **Scope**: User (global)

## Key Features
- **SQL query execution**: Direct database interaction through natural language
- **Table management**: Create and modify database structures
- **Business analytics**: Automated insights generation
- **Workspace integration**: Database file located in project directory

## Database Management

### Database Location
Current database: `/Volumes/Data/Software/2025/Acumen_Desktop/monorepo_july_2025/test.db`

### Basic Operations
```sql
-- Create table (via create_table tool)
CREATE TABLE users (id INTEGER PRIMARY KEY, name TEXT, email TEXT);

-- Read data (via read_query tool)  
SELECT * FROM users;

-- Write data (via write_query tool)
INSERT INTO users (name, email) VALUES ('John', '<EMAIL>');
```

## Integration with FAP Architecture
- **Development database**: Perfect for prototyping and testing
- **Simple data storage**: Lightweight alternative to TerminusDB for simple data
- **Local development**: No external dependencies or services
- **Version controllable**: Database file can be committed with code

## Troubleshooting

### Common Issues
1. **Path problems**: Ensure full absolute path to database file
2. **UV dependency**: Requires uv/uvx to be installed
3. **File permissions**: Database directory must be writable
4. **JSON escaping**: Use proper JSON format for complex arguments

### Debugging
```bash
# Check server status
claude mcp list

# Test database access (create the file if needed)
touch /path/to/database.db

# Verify uvx installation
uvx --help
```

## Use Cases
- **Rapid prototyping**: Quick database setup for development
- **Data analysis**: SQL queries through natural language
- **Testing scenarios**: Isolated database for unit tests
- **Configuration storage**: Application settings and metadata
- **Local data cache**: Temporary data storage during development

## Sub-Agent Learning
This server demonstrates:
- **JSON configuration** required for complex command arguments
- **uvx ecosystem** usage (consistent with git server pattern)
- **Path requirements** similar to filesystem server
- **Archived status** doesn't mean non-functional - just older/stable
- **Database file creation** - file doesn't need to exist initially

## Security Notes
- **Local access only**: Database file security relies on filesystem permissions
- **No network exposure**: SQLite is file-based, no network services
- **Simple authentication**: Access controlled by file system permissions
- **Development use**: Suitable for development, not production databases

## Performance Considerations
- **File-based**: Performance limited by disk I/O
- **Single writer**: SQLite supports concurrent reads but single writer
- **Small to medium data**: Optimal for < 1GB databases
- **ACID compliance**: Full transaction support for data integrity