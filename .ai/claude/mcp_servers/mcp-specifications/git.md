# Git MCP Server Specification

**Docs**: https://github.com/modelcontextprotocol/servers/tree/main/src/git  
**Status**: ✅ Connected and working  
**Installation**: `uvx mcp-server-git` (user scope)

## Overview
Model Context Protocol server for Git repository interaction and automation. Provides tools to read, search, and manipulate Git repositories via Large Language Models.

## Working Installation
```bash
claude mcp add --scope user git uvx mcp-server-git
```

**Key Discovery**: Use `uvx` instead of `npx @modelcontextprotocol/server-git`

## Available Tools
- **Git operations**: status, diff (staged/unstaged), commit, add, reset, log
- **Branch management**: creation, checkout
- **Repository automation**: designed for LLM workflows

## Installation Methods

### ✅ Working: UV/UVX
```bash
# Global installation
claude mcp add --scope user git uvx mcp-server-git
```

### ❌ Failed: NPX
```bash
# This doesn't work
claude mcp add --scope user git npx @modelcontextprotocol/server-git
```

## Configuration Notes
- **Early development stage** - functionality subject to change
- **No path required** - works with current directory context
- **UV dependency** - requires uv/uvx instead of npm/npx

## Troubleshooting
If connection fails:
1. Ensure `uvx` is available in PATH
2. Try with explicit repository path as argument
3. Use MCP inspector: `npx @modelcontextprotocol/inspector uvx mcp-server-git`
4. Check logs: `tail -n 20 -f ~/Library/Logs/Claude/mcp*.log`

## Development Notes
- **Docker build**: `cd src/git && docker build -t mcp/git .`
- **MIT License**
- **Designed for flexibility** across development environments

## Sub-Agent Learning
This server demonstrates the importance of:
- Testing multiple package managers (npx vs uvx)
- Reading official documentation for correct installation method
- Systematic retry with different approaches when standard patterns fail