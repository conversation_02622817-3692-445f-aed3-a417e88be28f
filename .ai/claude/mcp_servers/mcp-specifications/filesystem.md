# Filesystem MCP Server Specification

**Docs**: https://github.com/modelcontextprotocol/servers/tree/main/src/filesystem  
**Status**: ✅ Connected and working  
**Installation**: `npx @modelcontextprotocol/server-filesystem` (user scope)

## Overview
Official reference MCP server providing secure file operations with configurable access controls. Essential for development workflows requiring file manipulation.

## Working Installation
```bash
claude mcp add --scope user filesystem npx @modelcontextprotocol/server-filesystem /Volumes/Data/Software/2025/Acumen_Desktop
```

**Key Requirement**: Path argument specifying allowed directory access

## Available Tools
- **File operations**: Read, write, create, delete files
- **Directory operations**: List, create, navigate directories  
- **Security controls**: Access limited to specified path
- **Safe operations**: Built-in protections against path traversal

## Installation Pattern

### ✅ Working: With Path Argument
```bash
# Global installation with directory access
claude mcp add --scope user filesystem npx @modelcontextprotocol/server-filesystem /path/to/allowed/directory
```

### ❌ Failed: Without Path
```bash
# This fails - no directory specified
claude mcp add --scope user filesystem npx @modelcontextprotocol/server-filesystem
```

## Security Features
- **Access control**: Only operates within specified directory
- **Path validation**: Prevents directory traversal attacks
- **Safe defaults**: Conservative permissions for file operations

## Configuration Notes
- **Path required**: Must specify allowed directory as argument
- **Recursive access**: Can access all subdirectories within allowed path
- **Cross-platform**: Works on macOS, Linux, Windows

## Use Cases
- **Code analysis**: Reading source files for review
- **File generation**: Creating new files based on templates
- **Project organization**: Moving and organizing files
- **Content processing**: Reading/writing documentation, configs

## Sub-Agent Learning
This server demonstrates:
- Importance of reading parameter requirements in documentation
- Security-first design with explicit access controls
- Path arguments as common pattern for file system tools
- Testing with different path configurations when troubleshooting