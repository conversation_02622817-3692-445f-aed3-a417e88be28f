# Final MCP Server Process Documentation
*Tested and verified working process - 2025-07-28*

## What We Learned

### ❌ Previous Mistakes
1. **Wrong assumption**: Claude <PERSON> not available (it was in `/Users/<USER>/.claude/local/`)
2. **Manual JSON editing**: Unnecessary complexity 
3. **Mixed configurations**: Project + global configs causing confusion
4. **Incorrect syntax**: Used `--user` instead of `--scope user`

### ✅ Correct Process

## Working Setup (Final Status - 2025-07-28)

**CLI Location**: `/Users/<USER>/.claude/local/node_modules/.bin/claude`

**Current Global Servers**:
- ✅ **filesystem** - File operations (`npx @modelcontextprotocol/server-filesystem /path`)
- ✅ **git** - Repository management (`uvx mcp-server-git`) - FIXED!
- ✅ **sqlite** - Database operations (`uvx mcp-server-sqlite --db-path /path`) - NEW!
- ✅ **playwright** - Browser automation (`npx @playwright/mcp@latest`) 
- ❌ **Ref** - Documentation search (authentication issues persist)

## Commands That Work

### Add Server (Global Scope)
```bash
/Users/<USER>/.claude/local/node_modules/.bin/claude mcp add --scope user <name> npx @modelcontextprotocol/server-<name> [optional-path]
```

### Examples
```bash
# Filesystem with path
claude mcp add --scope user filesystem npx @modelcontextprotocol/server-filesystem /Volumes/Data/Software/2025/Acumen_Desktop

# Git operations (uvx pattern)
claude mcp add --scope user git uvx mcp-server-git

# SQLite database (JSON config for complex args)
claude mcp add-json --scope user sqlite '{"command": "uvx", "args": ["mcp-server-sqlite", "--db-path", "/path/to/database.db"]}'

# Playwright (no path needed)
claude mcp add --scope user playwright npx @playwright/mcp@latest
```

### Management Commands
```bash
# List all servers with health check
claude mcp list

# Remove server  
claude mcp remove <name>

# Get help
claude mcp --help
claude mcp add --help
```

## Key Insights

### Global vs Project Configuration
- **Global** (`--scope user`): Stored in `/Users/<USER>/.claude.json`
- **Project** (`.mcp.json`): Stored in project root, team-shared
- **We chose global** for universal access

### Server Requirements
- **Path arguments**: filesystem, sqlite need directory/file paths
- **Package managers**: git, sqlite use uvx (not npx)
- **JSON configuration**: Complex arguments require `claude mcp add-json`
- **Standalone**: playwright works immediately
- **Authentication**: Some servers (Ref) need valid API keys

## Sub-Agent Suitability

**Perfect for sub-agent because**:
1. **Isolated task** - Research, install, verify
2. **Resource efficient** - Doesn't clutter main session
3. **Reusable** - Same process for any future MCP server
4. **Specific expertise** - CLI commands, server evaluation, troubleshooting

**Sub-agent would**:
1. Research servers from official repo
2. Use proper CLI commands for installation  
3. Test with `claude mcp list`
4. Remove failed servers and retry with different configurations
5. Report back with working setup

## Lessons for Future

1. **Always check CLI availability** before assuming manual config needed
2. **Use proper scope syntax** (`--scope user` not `--user`)
3. **Test immediately** with `claude mcp list` after adding
4. **Consolidate to one scope** (global) to avoid confusion
5. **Sub-agents are perfect** for this type of isolated, technical task

---

*This process is now documented for future Claude sessions. Use sub-agent for new MCP server installations.*