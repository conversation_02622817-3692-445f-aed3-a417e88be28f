# Sub-Agent Management System

This project uses a self-managing sub-agent system located in `acumen-fap/ai/claude/agents/`. The naming convention is - singular "agent" for individual configs, plural "agents" for system-wide files.

## Core Files

- **`agents-index.md`** - Living registry of all available agents organized by domain
- **`agents-management-instructions.md`** - How to add, update, and remove agents
- **`agents-creation-guidelines.md`** - Standards and templates for creating quality agents
- **`_notes/claude-memory-overview.md`** - This overview file for CLAUDE.md integration

## Agent Configurations

Individual agent specifications in `agent-specifications/`:

- **`agent-manager-config.md`** - Creates and maintains agents and the index
- **`agent-settings-manager.md`** - Modifies Claude Code configuration files
- **`agent-markdown-specialist.md`** - Enhanced documentation formatting and living docs

## Slash Commands System

Located in `acumen-fap/ai/claude/slash_commands/`:

- **`slash-commands-index.md`** - Registry of reusable multi-agent workflows
- **`slash-commands-management-instructions.md`** - Command lifecycle management
- **`slash-commands-creation-guidelines.md`** - Standards for workflow commands
- **`commands-manager-config.md`** - Agent that creates and maintains slash commands

Commands are stored in `.claude/commands/` and available as `/project:namespace:command`.

## MCP Servers System

Located in `ai/claude/mcp_servers/`:

- **`mcp-servers-index.md`** - Curated registry of quality MCP servers by category
- **`mcp-servers-management-instructions.md`** - Server evaluation and lifecycle management
- **`mcp-servers-creation-guidelines.md`** - Quality standards and stack relevance criteria
- **`mcp-manager-config.md`** - Agent that evaluates and manages MCP server configurations

Servers are configured in `.mcp.json` (project-level) with focus on official/high-quality servers only.

## Usage

Use `/agents` to access the management interface or invoke agents explicitly: `"agent-name: task description"`.

Key agents for Acumen FAP infrastructure:

- `agent-manager` - Agent lifecycle management and index updates
- `commands-manager` - Creates and maintains slash command workflows
- `monorepo-architect` - PNPM workspace design and dependency management
- `docs-manager` - Technical documentation and API specifications
- `pear-specialist` - P2P application development using Holepunch ecosystem

All agents follow functional programming principles with ES6+ patterns, immutable data structures, and modern async/await syntax. The `agent-manager` can create new agents, update existing ones, and maintain the index. The `settings-manager` handles Claude Code configuration changes.
