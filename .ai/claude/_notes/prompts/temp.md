There seems to be serious confusion about the markdown files in 'acumen-fap/ai/claude', they are documentation files for the claude memory system, not actual claude code files. Yes, the actual claude code files will go in the .claude folder. In the 'acumen-fap/ai/claude' folder we track and manage our journey of setting up the actual agents, mcp and slash commands. These are a new feature for you and me, so we need to test them out. Yesterday, you had trouble accessing the MCP servers, let's try again today. Once that is working we can move on to agents and slash commands.

1. The documentation may be inacurate but if you read them carefully, you should see the logic of how they are set up.
2. We will work on MCP first.
3. ditto
4. Yes, we want you to be able to control all the sub agents, manage them, update them, remove them, etc.

Should .mcp.json be in project root? Or should it be in the '.claude' folder?
The 'playwright' and 'Ref' MCP servers may be defined in the global .claude.json file, but the 'filesystem' and 'git' MCP servers should be defined in the project .mcp.json file, and they are not currently showing up in '/mcp' commmand. I think they should all be in the local project .mcp.json file, which should be in '.claude'? Afer you confirm this, can you test all 5 MCP servers and let me know if they are working?

I will restart now, but first create a summary document of our Agent, Slash Command and MCP setup journey so far, and save it to: 'acumen-fap/ai/claude/\_notes'. Also update your 'CLAUDE.md' to be up to date.

In the last session, we were working on MCP servers, but we both made mistakes. Read the docs: 'https://docs.anthropic.com/en/docs/claude-code/mcp' and read our instruction files here: 'acumen-fap/ai/claude/mcp_servers', they are obvioulsy confusing. Try and figure out what our instructions are attempting to do, then clarify with me, then we can discuss, then we can edit our instruction MCP files, then we can try again.

I notice there was a failure to install the 'git' server. Maybe this is a good time to test the "Ref" MCP and see if it can find the correct documentation. If not, we need another way to troubleshoot failed MCP installs and give the "sub agent" super powers.
